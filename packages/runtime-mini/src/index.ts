

import  {
    SqbComponentOptions,
    IContext,
    XData,
    isWechatPlatform,
    ensureComponentOptions
} from "@wosai/smart-mp-concise-runtime-base"


export * from "./apis"

import {
    SqbAlipayComponent,
    SqbAlipayPage,
    SqbWechatComponent,
    SqbWechatPage
} from "./platform/index"


export function ConciseComponent(options: SqbComponentOptions = {}) {
        options.isConciseRoot = true
        //@ts-ignore
        return Component(getComponentOptions(options))
}

export function ConcisePage(options:any) {
        return isWechatPlatform() ? SqbWechatPage(options) : SqbAlipayPage(options)
}


export function ConciseApp(options) {

    return App(options)
}

export function getComponentOptions(options:SqbComponentOptions = {}): XData{

    let sqbOptions = {...options}
    ensureComponentOptions(sqbOptions)
    const context = createContext(sqbOptions);

    const platComponent = isWechatPlatform() ? new SqbWechatComponent(context) : new SqbAlipayComponent(context)

    platComponent.process();
    return platComponent.getOptions()

}


function createContext(options:SqbComponentOptions = {}):IContext {

    return {
        options,
        handler :{
            getComponentOptions,
            isRoot:() => !!options.isConciseRoot
        }
    }

}


// 转换微信的bahavior为alipay的,在编译的时候替换掉
 function transformWechatBehaviorToAlipay(options) {
        return getComponentOptions(options);

}



export function ConciseBehavior(options) {

    if (isWechatPlatform()) {
        return Behavior(options)
    }else {

        return Mixin(transformWechatBehaviorToAlipay(options))
    }

}
import { SqbBaseComponent } from "../base";


import {
    fill
} from "@wosai/smart-mp-concise-shared"
export class SqbWechatComponent extends SqbBaseComponent {

    process(): void {
        this.processProperties();
        this.processBehavior();
        this.processOther();
    }

    processOther(){
        let { options } = this.context;
        options.options = {
            multipleSlots: true,
        }
    }


    // 生命周期函数处理
    // 1、 代理this.data数据
    // 2、初始化td功能
    processInitLife() {

        const { options } = this.context;
        let self = this;
        fill(options, "attached", (origin:Function) => {
            return function (...args:any) {
                //@ts-ignore
                self.processTd(this);

                //@ts-ignore
                if (origin) return origin.call(this, ...args);
            }
           
        })

    }



}



export function SqbWechatPage(options) {

    //@ts-ignore
    return Page(options)

}
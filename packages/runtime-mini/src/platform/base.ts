import { 
    XData,
    IContext
 } from "@wosai/smart-mp-concise-runtime-base"


 import {
    isObject,
    compThrottled,
    debounce,
    logger
 } from "@wosai/smart-mp-concise-shared"





export class SqbBaseComponent {
    context:IContext
    constructor( context:IContext) {
        this.context = context
    }

    process(){

    }


    processProperties(){

        const { options  : { properties }} = this.context;
        
        properties.customStyle = {
            type:String
        }
    }

    getOptions():XData {
        return this.context.options
    }



    processBehavior(extraBehaviors:any[] = []) {
        let { options } = this.context;
        let { behaviors = [] } = options

        if (this.context.handler.isRoot()) {
             behaviors.unshift(...extraBehaviors)
        }
      

    }



    processTd(component:any) {
        let { options } = this.context;
        let { td = {}} = options;

        for (let method in td) {
            let item = td[method]
            if (!item) continue;
            let { type, delay = 800 } = item
            let handleMethod = type === "throttle" ? compThrottled:debounce;
            let temp = component[method]
            if (temp && typeof temp === "function") {
                component[method] = handleMethod(temp.bind(component),delay)
            }
        }
    }
}


export  namespace SqbBaseComponent {

    
}
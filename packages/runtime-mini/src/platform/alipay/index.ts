import { SqbBaseComponent } from "../base";

import {
    aliComponentMap,
    map<PERSON><PERSON><PERSON>,
    WePropertyConstructor,
    XData,
    mapWechatProperties
} from "@wosai/smart-mp-concise-runtime-base"

import {
    fill,
    logger
} from "@wosai/smart-mp-concise-shared"

import {
    createEmitBehavior
} from "./behaviors/emit"

import {
    setDataWatcher,
    hackSetData
} from "./watcher/setdata"

export class SqbAlipayComponent extends SqbBaseComponent {


    process(): void {
        this.processProperties();
        this.processLiftimes();
        this.processBehavior(
            [
                createEmitBehavior()
            ]
        );
        this.processInitLife();
        this.processOther();
    }




    processOther() {

        let { options } = this.context;
        options.options = {
            // 启用组件间关系定义段
            relations: true,
            observers: true,
            externalClasses:true
        }
    }

    // 处理props 将微信的格式转成支付宝的
    // 将属性observer移到支付宝的options里面

    processProperties() {
        super.processProperties();
        const { options } = this.context;
        let { properties, observers = {} } = options

        let  props = {};

        mapWechatProperties(properties!,{
            nullProcess:(key) => {
                props[key] = null
            },
            constructorProcess:(key,value) => {
                props[key] = (new value()).valueOf()
            },
            objectProcess:(key,item) => {
                const propertyObserver = item.observer;
                // 对类型进行observer监听
                if (propertyObserver) {
                    observers[key] = propertyObserver
                }
                props[key] = item.value

            }
        })
       
        options.properties = props;

    }


    getOptions(): XData {

        let alipayOptions = {}

        mapKeys(this.context.options, alipayOptions, aliComponentMap);
        return alipayOptions
    }


    //1 liftimes字段里，微信和支付宝是一致的，支付宝少了一个error函数支持
    //2 options 字段里，
    //     对应的是attached -> onInit
    //     对应的是ready -> didMount
    //     对应的是detached -> didUnmount
    //     对应的是error -> onError
    // 微信的moved和created函数不支持,这里做个提醒

    processLiftimes() {
        const { options } = this.context;
        const { error: optionsErrorFunction, lifetimes = {} } = options
        if (lifetimes.error) {
            // 生命周期中有error函数，则放到外面去
            fill(options, "error", function (origin: Function) {
                return function (...args: any) {
                    //@ts-ignore
                    lifetimes.error?.call(this, ...args);
                    //@ts-ignore
                    if (origin) return origin.call(this, ...args);
                }
            })

            delete lifetimes.error;

        }

        // 支付宝不支持moved，这里做个提醒
        if (options.moved) {
            logger.warn("组件的options中的moved函数，在支付宝中没有对应生命周期")
        }

        if (options.created) {
            logger.warn("组件的options中的created函数，在支付宝中没有对应生命周期")

        }


    }


    // 生命周期函数处理
    // 1、劫持setData
    // 2、初始化td功能
    processInitLife() {

        const { options } = this.context;
        const { properties = {} } = options;
        let self = this;
        let keys = Object.keys(properties);
        fill(options, "attached", (origin: Function) => {
            return function (...args: any) {
                //@ts-ignore
                self.processTd(this);

                 //@ts-ignore
                hackSetData.call(this,[setDataWatcher], { immediate : true })

                //@ts-ignore
                if (origin) return origin.call(this, ...args);
            }

        })

    }




}


export function SqbAlipayPage(options) {

    //@ts-ignore
    return Page(options)

}
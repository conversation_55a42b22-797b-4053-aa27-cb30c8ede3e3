



 function createEmitBehavior() {


  return Mixin({

    methods: {
        concise_emit(eventName:string, args:any) {

             eventName = eventName.replace(/\b(\w)(\w*)/g, function($0, $1, $2) {
                return $1.toUpperCase() + $2;
              });
      
              // 将参数进行分装
              const timeStamp = +new Date();
              const eventObj = {
                type: eventName,
                timeStamp,
                detail: args,
              };
              var funcName = `on${eventName}`;
              if (this.props[funcName]) {
                this.props[funcName](eventObj);
              }
        },

        triggerEvent(...args) {
          return this.concise_emit(...args);
        }
    }
})
 }




export {
  createEmitBehavior
}
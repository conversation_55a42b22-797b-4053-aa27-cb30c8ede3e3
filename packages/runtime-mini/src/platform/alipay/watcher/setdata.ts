


import { XData } from "@wosai/smart-mp-concise-runtime-base"

import {
    logger
} from "@wosai/smart-mp-concise-shared"

export function setDataWatcher() {

        let propKeys = Object.keys(this.props)
        for (const key of propKeys) {
            let _key = key;
            Object.defineProperty(this.data, _key, {
              configurable: true,
              get: () => {
                return this.props[_key];
              },
            });
          }

}


// 劫持setData
export function hackSetData(watchers:any[] = [] , options:XData = {}) {

  const originalSetData = this.setData
  if (!originalSetData) {
    logger.error(`劫持 setData 失败, 可能导致无法正确触发更新`)
  }

  function immediateWatchers() {

    watchers.forEach((watcher) => {
        watcher.call(this)
     })
  }

  if (options.immediate) {
        immediateWatchers.call(this)
  }

  this.setData = (
    nextData: Record<string, any> = {},
    callback?: () => void
  ): void => {

     originalSetData.call(this, nextData, callback)

     immediateWatchers.call(this)
  }
}
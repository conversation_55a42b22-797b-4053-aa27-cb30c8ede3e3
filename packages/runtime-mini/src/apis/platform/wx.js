import { cacheDataSet } from '../data-cache'
import { queryT<PERSON><PERSON><PERSON>, getUni<PERSON><PERSON><PERSON> } from '../utils'

import { noPromiseApis, onAndSyncApis, otherApis } from '../native-apis'

function processApis (sqb) {
  const weApis = Object.assign({}, onAndSyncApis, noPromiseApis, otherApis)
  const useDataCacheApis = {
    navigateTo: true,
    redirectTo: true,
    reLaunch: true
  }
  const routerParamsPrivateKey = '__key_'
  Object.keys(weApis).forEach(key => {
    if (!(key in wx)) {
      sqb[key] = () => {
        console.warn(`微信小程序暂不支持 ${key}`)
      }
      return
    }

    if (!onAndSyncApis[key] && !noPromiseApis[key]) {
      sqb[key] = (options, ...args) => {
        options = options || {}
        let task = null
        const obj = Object.assign({}, options)
        if (typeof options === 'string') {
          if (args.length) {
            return wx[key](options, ...args)
          }
          return wx[key](options)
        }

        if (key === 'navigateTo' || key === 'redirectTo' || key === 'switchTab') {
          let url = obj.url ? obj.url.replace(/^\//, '') : ''
          if (url.indexOf('?') > -1) url = url.split('?')[0]
        }

        if (useDataCacheApis[key]) {
          const url = (obj.url = obj.url || '')
          const MarkIndex = url.indexOf('?')
          const hasMark = MarkIndex > -1
          const urlQueryStr = hasMark ? url.substring(MarkIndex + 1, url.length) : ''
          const params = queryToJson(urlQueryStr)
          const cacheKey = getUniqueKey()
          obj.url += (hasMark ? '&' : '?') + `${routerParamsPrivateKey}=${cacheKey}`
          cacheDataSet(cacheKey, params)
        }

        const p = new Promise((resolve, reject) => {
          ['fail', 'success', 'complete'].forEach(k => {
            obj[k] = res => {
              options[k] && options[k](res)
              if (k === 'success') {
                if (key === 'connectSocket') {
                  resolve(Promise.resolve().then(() => Object.assign(task, res)))
                } else {
                  resolve(res)
                }
              } else if (k === 'fail') {
                reject(res)
              }
            }
          })
          if (args.length) {
            task = wx[key](obj, ...args)
          } else {
            task = wx[key](obj)
          }
        })
        if (key === 'uploadFile' || key === 'downloadFile') {
          p.progress = cb => {
            if (task) {
              task.onProgressUpdate(cb)
            }
            return p
          }
          p.abort = cb => {
            cb && cb()
            if (task) {
              task.abort()
            }
            return p
          }
        }
        return p
      }
    } else {
      sqb[key] = (...args) => {
        //  const argsLen = args.length
        const newArgs = args.concat()
        // const lastArg = newArgs[argsLen - 1]
        // if (lastArg && lastArg.isTaroComponent && lastArg.$scope) {
        //   newArgs.splice(argsLen - 1, 1, lastArg.$scope)
        // }
        return wx[key].apply(wx, newArgs)
      }
    }
  })
}

export default function initWxApi (sqb) {
  processApis(sqb)
}

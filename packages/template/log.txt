
> @wosai/smart-mp-concise-template@1.0.37 dev
> concise compile -w

Server started: http://192.168.3.11:8081
▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄
█ ▄▄▄▄▄ █▄▄▄ ▀ ▀█▄█ ▄▄▄▄▄ █
█ █   █ ██▄▀ █  ▀▀█ █   █ █
█ █▄▄▄█ ██▀▄ ▄█████ █▄▄▄█ █
█▄▄▄▄▄▄▄█ ▀▄█ ▀▄▀▄█▄▄▄▄▄▄▄█
█  █  █▄▀█▄▀█▄▀█ ▀▀▄█▀▀▀▀▄█
█ ▀▀▄▀ ▄█▄▄██▄▄▄ █ ▄▄  ▀▀ █
█▀ ▀▄▀▀▄▀ ▄ █▀█▄▀▄▄▄▀▀██▀▄█
█ ▄▀██ ▄ █▀▀█▀▄▀▄ ▄██▀▄ ▄ █
█▄█▄███▄█ ▄▄ ▄▄ ▀ ▄▄▄ █▄ ██
█ ▄▄▄▄▄ ████▀▄  █ █▄█ ▄██ █
█ █   █ █ █▄▄ ▀█▄▄▄  ▄ █▀▀█
█ █▄▄▄█ █▀▀  ▀█▄▀█▀▀▀ █   █
█▄▄▄▄▄▄▄█▄██▄██▄▄▄█▄██▄██▄█

dev /"[^"]+"|'[^']+'|url\([^\)]+\)|(\d*\.?\d+)rpx/g 20rpx 2.66667vw vw 750
dev /"[^"]+"|'[^']+'|url\([^\)]+\)|(\d*\.?\d+)rpx/g 36rpx 4.8vw vw 750
dev /"[^"]+"|'[^']+'|url\([^\)]+\)|(\d*\.?\d+)rpx/g 20rpx 2.66667vw vw 750
dev /"[^"]+"|'[^']+'|url\([^\)]+\)|(\d*\.?\d+)rpx/g 50rpx 6.66667vw vw 750
dev /"[^"]+"|'[^']+'|url\([^\)]+\)|(\d*\.?\d+)rpx/g 12rpx 1.6vw vw 750
assets by path npm/ 3.15 MiB 165 assets
assets by path *.js 589 KiB
  asset app.js 546 KiB [emitted] (name: app.js)
  asset concise.r.js 43.3 KiB [emitted] (name: concise.r.js)
asset index.html 12.3 KiB [emitted]
Entrypoint app.js 3.73 MiB = 167 assets
runtime modules 28.7 KiB 16 modules
orphan modules 922 bytes [orphan] 1 module
modules by path ../ 2.9 MiB
  modules by path ../node_modules/@wosai/smart-mp-concise-runtime-web/ 2.47 MiB 108 modules
  modules by path ../../../node_modules/ 448 KiB 66 modules
modules by path ./ 456 KiB
  javascript modules 455 KiB
    modules by path ./pages/home/<USER>
    modules by path ./*.less 4.15 KiB 2 modules
    ./app.ts 1.56 KiB [built] [code generated]
    ./util.inspect (ignored) 15 bytes [built] [code generated]
  json modules 535 bytes
    ./app.json 40 bytes [built] [code generated]
    ./pages/home/<USER>
webpack 5.93.0 compiled successfully in 6041 ms

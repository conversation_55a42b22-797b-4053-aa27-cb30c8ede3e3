
## template例子


### 编译
npm install
npm run build或者npm run dev
在目标目录产生一个js产物，在自己的项目的index.html引入该js，即可以使用相应组件

### 说明
目前只支持web的的编译，包括web组件化

### 配置

在项目的concise.config.js 进行配置，目前支持js配置文件，不支持ts

 {

        target:"web",
        outputPath:"dist/web_components",
        compileType : "component", // 组件库
        alias:{a:"../a.js"},
        plugins:[]// webpack 的plugin 。
}

target:编译的平台,
outputPath:输出目录，如果不指定，则默认为dist/${target}
compileType:component或者miniprogram
alias:路径别名

目前配置文件不支持传自定义插件

### 使用方式
1.当编译组件库的时候，需要在src目录下定义component.json。配置如下：
{

    "publicComponents":{
        "home-info":"components/info/index" 
    },
    "main":"index.js"
}


publicComponents 可以是数组或者对象，如果是对象的话，key代表组件在浏览器的标签名字，
main字段需要指定，是入口文件，

当编译组件库的时候，在目标目录会生成一个文件

在自己的index.html里加载这个js文件

```
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name='viewport' content='width=device-width,initial-scale=1.0,minimum-scale=1,maximum-scale=1,user-scalable=no'>

    <style>
      body{
        margin: 0px;
      }
    </style>
   
    <title></title>
   
    <script src="index.js"></script>
    <!--preload-links-->
    <!--app-context-->
  </head>
  <body>
    <home-info 
     id="homeInfoComponent"
      title = "Concise"
    />


  </body>

  <script >


      const homeInfo = {
        name: "Xukaijie",
        nickName: "Bajie",
      };

      // Find the component and set the property
      const component = document.getElementById('homeInfoComponent');
      component.info = homeInfo;

  </script>
</html>

```

### 注意方式
html里元素只能传字符串，所以如果你的组件的属性是对象的话，从html里是无法传入的，需要通过js的方式传递进去，即标签属性和js属性的区别。

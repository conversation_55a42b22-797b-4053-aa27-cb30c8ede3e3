

const {
    defineConfig
} = require("@wosai/smart-mp-concise-cli")


const path = require("path")

const _ = require("lodash")
const HtmlWebpackPlugin = require('html-webpack-plugin')


class WebpackPostcssPlugins {
    apply(compiler) {
        compiler.hooks.normalModuleFactory.tap('CustomPlugin', normalModuleFactory => {
            normalModuleFactory.hooks.afterResolve.tap('CustomPlugin', ({ createData }) => {
                const { loaders = [] } = createData

                const postcssLoader = _.find(loaders, { ident: 'postcss-loader' })

                if (postcssLoader && postcssLoader.options) {
                    postcssLoader.options.postcssOptions.plugins.unshift(
                        require('postcss-px-to-viewport')({
                            unitToConvert: 'rpx', // 需要转换的单位
                            viewportWidth: 750, // 视口宽度，对应设计稿宽度
                            unitPrecision: 5, // 转换后的精度，即小数点位数
                            propList: ['*'], // 需要转换的属性列表
                            viewportUnit: 'vw', // 转换后的单位
                            fontViewportUnit: 'vw', // 字体转换后的单位
                            selectorBlackList: [], // 忽略的CSS选择器
                            minPixelValue: 0, // 小于或等于0rpx不转换
                            mediaQuery: false, // 是否转换媒体查询中的px
                            replace: true, // 是否直接替换属性值，而不添加备用属性
                            // exclude: [], // 忽略的文件或目录
                            landscape: false // 是否处理横屏情况
                        }),


                        //   require('postcss-pxtorem')({
                        //     rootValue: 16,          // 根字体大小，通常设置为16px（1rem = 16px）
                        //     unitPrecision: 5,       // 转换后的小数点精度
                        //     propList: ['*'],        // 需要转换的属性，* 表示所有属性
                        //     mediaQuery: false,      // 是否处理媒体查询
                        //     minPixelValue: 1,       // 最小的像素值，单位转换不会小于这个值
                        //   })

                    )
                }
            })
        })
    }
}


function getAlias() {

    return {
        "@style": path.resolve(__dirname, "./src/less"),
        "@wxs": path.resolve(__dirname, "./src/wxs"),
        "@template": path.resolve(__dirname, "./src/template"),
    }
}

module.exports = defineConfig([
    // {
    //     customEntries:{
    //         "component.json":"./src/component.web.json"
    //     },
    //     target: 'web',
    //     compileType: 'component',
    //     alias:{}
    // },

    // {
    //     target: 'wechat',
    //     compileType: 'miniprogram',
    //     alias:getAlias()
    // },


    // {
    //     target: 'alipay',
    //     customEntries:{
    //         "component.json":"./component.order.json"
    //     },
    //     compileType: 'component',
    //     alias:getAlias()
    // },

    {

        target: "web",
        compileType: "miniprogram",
        alias: getAlias(),
        plugins: [
            new WebpackPostcssPlugins(),
            new HtmlWebpackPlugin({
                title: '扫码点单H5',
                filename: 'index.html',
                template: './index.html'
            })
        ]
    }
])
/* Grid Container */
.grid {
  display: grid;
}

/* 使用 minmax 的响应式列布局 */
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}
.grid-cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}
.grid-cols-6 {
  grid-template-columns: repeat(6, minmax(0, 1fr));
}

/* Column Span */
.col-span-1 {
  grid-column: span 1;
}
.col-span-2 {
  grid-column: span 2;
}
.col-span-3 {
  grid-column: span 3;
}
.col-span-4 {
  grid-column: span 4;
}
.col-span-5 {
  grid-column: span 5;
}
.col-span-6 {
  grid-column: span 6;
}
.col-span-full {
  grid-column: 1 / -1;
}

/* Row Span */
.row-span-1 {
  grid-row: span 1;
}
.row-span-2 {
  grid-row: span 2;
}
.row-span-3 {
  grid-row: span 3;
}
.row-span-4 {
  grid-row: span 4;
}
.row-span-full {
  grid-row: 1 / -1;
}

/* Auto Flow */
.grid-flow-row {
  grid-auto-flow: row;
}
.grid-flow-col {
  grid-auto-flow: column;
}

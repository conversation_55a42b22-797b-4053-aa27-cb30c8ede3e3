.flex-row,
.flex-column,
.flex-center,
.flex-center-x,
.flex-center-y,
.flex {
  display: flex;
}
.inline-flex {
  display: inline-flex !important;
}
.flex-row {
  flex-direction: row;
}

.flex-column {
  flex-direction: column;
}

.flex-reverse {
  flex-direction: row-reverse;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-center {
  align-items: center;
  justify-content: center;
}

.flex-center-x,
.justify-center {
  justify-content: center;
}

.flex-center-y,
.items-center {
  align-items: center;
}

.flex-between {
  justify-content: space-between;
}

.flex-left {
  justify-content: flex-start;
}

.flex-right {
  justify-content: flex-end;
}

.flex-end {
  align-items: flex-end;
}
.flex-start {
  align-items: flex-start;
}
.flex-item {
  flex: 1;
}

.flex-row > .flex-item {
  width: 1px; /* no */
}

.flex-shrink {
  flex-shrink: 0;
}
.flex-initial {
  flex: 0 1 auto;
}
.flex-baseline {
  align-items: baseline;
}
.flex-baseline {
  align-items: baseline;
}
.flex-none {
  flex: none;
}
.flex-initial {
  flex: initial;
}

.flex-1 {
  flex: 1 1 0%;
}

.gap-16 {
  gap: 16px;
}

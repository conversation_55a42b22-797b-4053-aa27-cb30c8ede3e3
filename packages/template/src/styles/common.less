/* prettier-ignore */
@import (css) './font';

.smart-page {
  // 字体
  --font-family-lato: 'Lato', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  --font-family-lato-bold: 'Lato-Bold', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  --font-family-default: 'PingFang SC', 'PingFangSC' -apple-system, 'Segoe UI', Roboto,
    'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;

  //安全区域
  --safe-bottom: constant(safe-area-inset-bottom) / 2;
  --safe-bottom: env(safe-area-inset-bottom) / 2;
  // 自定义主题色
  --gray-color-999: #999;
  --discount-price-color: #ff4d4d;
  // ----------------
  --primary__color: #ff6a16;
  --primary-foreground__color: #ffffff;
  --primary-gradient__color: linear-gradient(270deg, #ff6c17 0%, #ff9434 100%);
  --primary-gradient-foreground__color: #ffffff;
  --secondary__color: #ff4d4d;
  --secondary-foreground__color: #ffffff;
  --primary-selected__color: rgba(255, 106, 22, 0.1);
  --discount-tag__color: #bf1100;
  // "component_id": "1a2b3c4d5e6f",
  // "component_name": "头部信息",
  --main__bg-img-1a2b3c4d5e6f: url(https://marketing-static.shouqianba.com/gallery/59c8ee627ccc2453d96d1a48c3b93e25.png);
  // "component_name": "分类导航",
  --hot-sale-icon__bg-img-9g8h7i6j5k4l: url(https://marketing-static.shouqianba.com/gallery/01b4660f038de08b56036ae4719af017.png);
  --recommend-icon__bg-img-9g8h7i6j5k4l: url(https://marketing-static.shouqianba.com/gallery/e5ee5f5528e616f508c41aba1922131c.png);
  --discount-icon__bg-img-9g8h7i6j5k4l: url(https://marketing-static.shouqianba.com/gallery/18d0e669e4831ea595e0a212daf703f4.png);
  --hot-sale-label__bg-img-9g8h7i6j5k4l: url(https://marketing-static.shouqianba.com/gallery/5e5b9d30d1860591f6cf67809b3bc6cf.png);
  --recommend-goods-label__bg-img-9g8h7i6j5k4l: url(https://marketing-static.shouqianba.com/gallery/5b0af1c1b7e4cdc7bb017dcb24416b99.png);
  // "component_id": "3m2n1o0p9q8r",
  // "component_name": "商品信息", // 热卖
  --goods-info__bg-img-3m2n1o0p9q8r: url(https://marketing-static.shouqianba.com/gallery/46bc9eb57f3f3bbc06929197578e5649.png);

  // 门店销量标签
  // style1
  --tag__bg-color-style1-3m2n1o0p9q8r: linear-gradient(90deg, #ff6c17 0%, #ff9434 100%);
  --tag__color-style1-3m2n1o0p9q8r: #ffffff;
  // style2
  --tag__bg-color-style2-3m2n1o0p9q8r: #ffece1;
  --tag__color-style2-3m2n1o0p9q8r: #333;
  --tag__border-color-style2-3m2n1o0p9q8r: #333;
  --tag__bg-img-style2-3m2n1o0p9q8r: url(https://marketing-static.shouqianba.com/gallery/bdc4d2f62a5f747f583ae7b8c55a21f1.png);
  // style3
  --tag-right__color-style3-3m2n1o0p9q8r: #bf1100;
  --tag-right__bg-color-style3-3m2n1o0p9q8r: #ffffff;
  --tag-left__color-style3-3m2n1o0p9q8r: #ffffff;
  --tag-left__bg-color-style3-3m2n1o0p9q8r: #bf1100;

  // "component_id": "7s6t5u4v3w2x",
  // "component_name": "商品推荐",
  --recommend-goods-icon__bg-img-7s6t5u4v3w2x: url(https://marketing-static.shouqianba.com/gallery/b6d2d4f4b51f981dcee1a05c67ae3a5a.png);

  // "component_id": "1y2z3a4b5c6d",
  // "component_name": "结算栏",
  --cart__bg-img-1y2z3a4b5c6d: url(https://marketing-static.shouqianba.com/gallery/ffe9c22856acf8d6b6d8c85fa767825f.png);
  --backdrop-filter-1y2z3a4b5c6d: 0;
  --bg-color-1y2z3a4b5c6d: #ffffff;
  --price__color-1y2z3a4b5c6d: #000;
  --border-color-1y2z3a4b5c6d: #fff;

  // "component_id": "9e8f7g6h5i4j",
  // "component_name": "加料推荐",
  --recommend-material-tag__bg-img-9e8f7g6h5i4j: url(https://marketing-static.shouqianba.com/gallery/3b4b9d5f56d9587e4d2ec13f02bb1287.png);

  // "component_id": "3k2l1m0n9o8p",
  // "component_name": "提交订单页背景",
  --main__bg-img-3k2l1m0n9o8p: url(https://marketing-static.shouqianba.com/gallery/59c8ee627ccc2453d96d1a48c3b93e25.png);

  // "component_id": "7q6r5s4t3u2v",
  // "component_name": "就餐方式",
  --pack-icon__bg-img-7q6r5s4t3u2v: url(https://marketing-static.shouqianba.com/gallery/b7a2b438d22a4c86d3bd1505faf31a25.png);
  --dinein-icon__bg-img-7q6r5s4t3u2v: url(https://marketing-static.shouqianba.com/gallery/bcc08e422ac9650321bb1023d51288a6.png);

  // "component_id": "1w2x3y4z5a6b",
  // "component_name": "加料组件",
  --recommend-material-icon__bg-img-1w2x3y4z5a6b: url(https://marketing-static.shouqianba.com/gallery/174a120120046cb9aae8d0464c894e63.png);

  // "component_id": "7l7kmqaopfpl",
  // "component_name": "就餐方式弹窗",
  --subscriber-icon__bg-img-7l7kmqaopfpl: url(https://marketing-static.shouqianba.com/gallery/db6fcfd7d3b8f7029c16e5acb02e90a8.png);
  --takeout-icon__bg-img-7l7kmqaopfpl: url(https://marketing-static.shouqianba.com/gallery/2b40ed97bef6d844d450be6a2728b32f.png);
  --pre-icon__bg-img-7l7kmqaopfpl: url(https://marketing-static.shouqianba.com/gallery/87deb1239069529767317d887f105d61.png);

  // "component_id": "9d5sttb89yix",
  // "component_name": "门店首页背景",
  --bg-img-9d5sttb89yix: url(https://marketing-static.shouqianba.com/template/2ab606502967eb29e79c5bea225a289a.png);
  --bg-color-9d5sttb89yix: #f5f5f5;

  // "component_id": "d74yeseqrz7w",
  // "component_name": "商铺信息", // 店铺信息 header
  --bg-color-d74yeseqrz7w: #fff;
  --content__color-d74yeseqrz7w: #333;
  --icon__color-d74yeseqrz7w: #fff;
  --title__color-d74yeseqrz7w: #333;

  // "component_id": "q3ny8z6yr2ub",
  // "component_name": "服务列表",
  --dinein-icon__bg-img-q3ny8z6yr2ub: url(https://marketing-static.shouqianba.com/template/f30e0cf9f1eff3e423ebd89d5c4be3ce.png);
  --takeout-icon__bg-img-q3ny8z6yr2ub: url(https://marketing-static.shouqianba.com/template/85dfd67af2d4787b3256cf281ebb1bd9.png);
  --pre-icon__bg-img-q3ny8z6yr2ub: url(https://marketing-static.shouqianba.com/template/d7cc96371b735dd0e7f63c03a2c0f385.png);
  --pay-icon__bg-img-q3ny8z6yr2ub: url(https://marketing-static.shouqianba.com/template/7396c78e7393b23f1b4057238fa4f805.png);
  --item-title__color-q3ny8z6yr2ub: #000;
  --item-content__color-q3ny8z6yr2ub: #999;
  --item-primary__bg-color-q3ny8z6yr2ub: #fff;
  --item-secondary__bg-color-q3ny8z6yr2ub: #fff;
  --item-arrow-icon_color-q3ny8z6yr2ub: #d8d8d8;

  font-family: var(--font-family-default);
}

.primary-color {
  color: var(--primary__color);
}

.primary-foreground-color {
  color: var(--primary-foreground__color);
}

.primary-gradient-color {
  color: var(--primary-gradient__color);
}

.primary-gradient-foreground-color {
  color: var(--primary-gradient-foreground__color);
}

.secondary-color {
  color: var(--secondary__color);
}

.secondary-foreground-color {
  color: var(--secondary-foreground__color);
}

.primary-selected-color {
  color: var(--primary-selected__color);
}

.discount-tag-color {
  color: var(--discount-tag__color);
}

.gray-color-999,
.color-999 {
  color: var(--gray-color-999);
}

.color-666 {
  color: var(--gray-color-666, #666);
}

.common-hover {
  opacity: 0.7;

  &.common-hover--disabled {
    opacity: 1;
  }

  &-disabled {
    opacity: 1;
  }
}

.color-white {
  color: #fff;
}

// ======================== 背景颜色 ===================================
.bg-f5 {
  background-color: #f5f5f5;
}

.bg-f6 {
  background-color: #f6f6f6;
}

.bg-ccc {
  background-color: #cccccc;
}

.bg-fd6d05 {
  background-color: #fd6d05;
}

.bg-white {
  background-color: #fff;
}

.bg-black {
  background-color: #000;
}

.bg-lightblack {
  background-color: #333;
}

.bg-gray {
  background-color: #666;
}

.bg-lightgray {
  background-color: #999;
}

.bg-orange {
  background-color: #ff6a16;
}

.bg-none {
  background: transparent;
}

.bg-img {
  background-repeat: no-repeat;
  background-size: contain;
  // background-size: 100% auto;
  background-position: center;
}

// ======================== MARGIN ===================================
.mg-0 {
  margin: 0;
}

.mt-0 {
  margin-top: 0;
}

.mr-0 {
  margin-right: 0;
}

.mb-0 {
  margin-bottom: 0;
}

.ml-0 {
  margin-left: 0;
}

.mr-2 {
  margin-right: 2px;
}

.ml-2 {
  margin-left: 2px;
}

.mr-4 {
  margin-right: 4px;
}

.ml-4 {
  margin-left: 4px;
}

.mt-4 {
  margin-top: 4px;
}

.mx-4 {
  margin-left: 4px;
  margin-right: 4px;
}

.mg-6 {
  margin: 6px;
}

.mt-6 {
  margin-top: 6px;
}

.mr-6 {
  margin-right: 6px;
}

.mb-6 {
  margin-bottom: 6px;
}

.ml-6 {
  margin-left: 6px;
}

.mg-8 {
  margin: 8px;
}

.mt-8 {
  margin-top: 8px;
}

.mr-8 {
  margin-right: 8px;
}

.mb-8 {
  margin-bottom: 8px;
}

.ml-8 {
  margin-left: 8px;
}

.mg-10 {
  margin: 10px;
}

.mt-10 {
  margin-top: 10px;
}

.mr-10 {
  margin-right: 10px;
}

.mb-10 {
  margin-bottom: 10px;
}

.ml-10 {
  margin-left: 10px;
}

.mg-12 {
  margin: 12px;
}

.mt-12 {
  margin-top: 12px;
}

.mr-12 {
  margin-right: 12px;
}

.mb-12 {
  margin-bottom: 12px;
}

.ml-12 {
  margin-left: 12px;
}

.mt-14 {
  margin-top: 14px;
}

.mb-14 {
  margin-bottom: 14px;
}

.mg-16 {
  margin: 16px;
}

.mt-16 {
  margin-top: 16px;
}

.mr-16 {
  margin-right: 16px;
}

.mb-16 {
  margin-bottom: 16px;
}

.ml-16 {
  margin-left: 16px;
}

.mg-18 {
  margin: 18px;
}

.mt-18 {
  margin-top: 18px;
}

.mr-18 {
  margin-right: 18px;
}

.mb-18 {
  margin-bottom: 18px;
}

.ml-18 {
  margin-left: 18px;
}

.mg-20 {
  margin: 20px;
}

.mt-20 {
  margin-top: 20px;
}

.mr-20 {
  margin-right: 20px;
}

.mb-20 {
  margin-bottom: 20px;
}

.ml-20 {
  margin-left: 20px;
}

.mx-20 {
  margin-left: 20px;
  margin-right: 20px;
}

.mg-24 {
  margin: 24px;
}

.mt-24 {
  margin-top: 24px;
}

.mr-24 {
  margin-right: 24px;
}

.mb-24 {
  margin-bottom: 24px;
}

.ml-24 {
  margin-left: 24px;
}

.mr-24 {
  margin-right: 24px;
}

.mx-24 {
  margin-left: 24px;
  margin-right: 24px;
}

.mg-30 {
  margin: 30px;
}

.mb-26 {
  margin-bottom: 26px;
}

.mt-26 {
  margin-top: 26px;
}

.mg-30 {
  margin: 30px;
}

.mt-30 {
  margin-top: 30px;
}

.mr-30 {
  margin-right: 30px;
}

.mb-30 {
  margin-bottom: 30px;
}

.ml-30 {
  margin-left: 30px;
}

.mx-30 {
  margin-left: 30px;
  margin-right: 30px;
}

.mt-32 {
  margin-top: 32px;
}

.mr-32 {
  margin-right: 32px;
}

.mb-32 {
  margin-bottom: 32px;
}

.mt-34 {
  margin-top: 34px;
}

.mt-36 {
  margin-top: 36px;
}

.mg-40 {
  margin: 40px;
}

.mt-40 {
  margin-top: 40px;
}

.mr-40 {
  margin-right: 40px;
}

.mb-40 {
  margin-bottom: 40px;
}

.ml-40 {
  margin-left: 40px;
}

.mt-46 {
  margin-top: 46px;
}

.mt-80 {
  margin-top: 80px;
}

.mr-auto {
  margin-right: auto;
}

.mr--30 {
  margin-right: -30px;
}
.mx--30 {
  margin: 0 -30px;
}

.m-auto {
  margin: 0 auto;
}

// ======================== PADDING ===================================
.pd-0 {
  padding: 0;
}

.pr-0 {
  padding-right: 0;
}

.p-20 {
  padding: 20px;
}

.pl-0 {
  padding-left: 0;
}

.py-2 {
  padding: 2px 0;
}

.pd-6 {
  padding: 6px;
}

.pr-6 {
  padding-right: 6px;
}

.pl-6 {
  padding-left: 6px;
}

.pt-6 {
  padding-top: 6px;
}

.pb-6 {
  padding-bottom: 6px;
}

.py-6 {
  padding: 6px 0;
}

.pd-8 {
  padding: 8px;
}

.pr-8 {
  padding-right: 8px;
}

.pl-8 {
  padding-left: 8px;
}

.px-8 {
  padding-left: 8px;
  padding-right: 8px;
}

.py-520 {
  padding: 5px 20px;
}

.pd-10 {
  padding: 10px;
}

.pr-10 {
  padding-right: 10px;
}

.pl-10 {
  padding-left: 10px;
}

.pt-10 {
  padding-top: 10px;
}

.pb-10 {
  padding-bottom: 10px;
}

.px-10 {
  padding-left: 10px;
  padding-right: 10px;
}

.py-10 {
  padding: 10px 0;
}

.pd-12 {
  padding: 12px;
}

.pr-12 {
  padding-right: 12px;
}

.pl-12 {
  padding-left: 12px;
}

.pt-12 {
  padding-top: 12px;
}

.px-12 {
  padding-left: 12px;
  padding-right: 12px;
}

.py-12 {
  padding: 12px 0;
}

.pd-16 {
  padding: 16px;
}

.pt-16 {
  padding-top: 16px;
}

.pb-16 {
  padding-bottom: 16px;
}

.pr-16 {
  padding-right: 16px !important;
}

.pl-16 {
  padding-left: 16px;
}

.pd-18 {
  padding: 18px;
}

.pr-18 {
  padding-right: 18px;
}

.pl-18 {
  padding-left: 18px;
}

.py-18 {
  padding-top: 18px;
  padding-bottom: 18px;
}

.pd-20 {
  padding: 20px;
}

.pt-20 {
  padding-top: 20px;
}

.pr-20 {
  padding-right: 20px;
}

.pl-20 {
  padding-left: 20px;
}

.px-20 {
  padding-left: 20px;
  padding-right: 20px;
}

.py-20 {
  padding-top: 20px;
  padding-bottom: 20px;
}

.px-24 {
  padding-left: 24px;
  padding-right: 24px;
}

.pb-26 {
  padding-bottom: 26px;
}

.py-26 {
  padding-top: 26px;
  padding-bottom: 26px;
}

.pd-30 {
  padding: 30px;
}

.pt-30 {
  padding-top: 30px;
}

.pr-30 {
  padding-right: 30px;
}

.pl-30 {
  padding-left: 30px;
}

.pb-20 {
  padding-bottom: 20px;
}

.pb-30 {
  padding-bottom: 30px;
}

.pt-32 {
  padding-top: 32px;
}

.pb-32 {
  padding-bottom: 32px;
}

.px-30 {
  padding-left: 30px;
  padding-right: 30px;
}

.py-32 {
  padding-top: 32px;
  padding-bottom: 32px;
}

.pt-36 {
  padding-top: 36px;
}

.pb-36 {
  padding-bottom: 36px;
}

.pd-40 {
  padding: 40px;
}

.pr-40 {
  padding-right: 40px;
}

.pl-40 {
  padding-left: 40px;
}

.px-40 {
  padding-left: 40px;
  padding-right: 40px;
}

// ======================== 位置 ===================================
.fixed {
  position: fixed;
}

.absolute {
  position: absolute !important;
}

.-left-10 {
  left: -10px;
}

.relative {
  position: relative;
}

.disabled {
  pointer-events: none;
  opacity: 0.5;
}

.abs-full {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}

.sticky {
  /* @minipack-ignore */
  position: sticky;
  position: -webkit-sticky;
  //top: 0;
}

.position-xy {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

// ======================== 字体 FONT-FAMILY ===================================
.font-medium {
  font-family: PingFangSC-Medium, PingFang SC;
}

.font-regular {
  font-family: PingFangSC-Regular, PingFang SC;
}

.lato,
.lato-regular {
  //font-family: 'Lato', PingFang SC, Helvetica Neue, Arial, sans-serif;
  font-family: var(--font-family-lato);
}

.lato-bold {
  font-weight: bold;
  //font-family: 'Lato-Bold', PingFang SC, Helvetica Neue, Arial, sans-serif;
  font-family: var(--font-family-lato-bold);
}

.helvetica {
  font-family: 'Helvetica', PingFang SC, Helvetica Neue, Arial, sans-serif;
}

.helvetica-light {
  font-family: 'Helvetica Light', PingFang SC, Helvetica Neue, Arial, sans-serif;
}

.helvetica-bold {
  font-weight: bold;
  font-family: 'Helvetica Bold', PingFang SC, Helvetica Neue, Arial, sans-serif;
}

// ======================== 字体大小 FONT-SIZE ===================================
.text-16 {
  font-size: 16px;
}

.text-20 {
  font-size: 20px;
}

.text-22 {
  font-size: 22px;
}

.text-24 {
  font-size: 24px;
}

.text-26 {
  font-size: 26px;
}

.text-28 {
  font-size: 28px;
}

.text-30 {
  font-size: 30px;
}

.text-32 {
  font-size: 32px;
}

.text-34 {
  font-size: 34px;
}

.text-36 {
  font-size: 36px;
}

.text-40 {
  font-size: 40px;
}

.text-42 {
  font-size: 42px;
}

.text-44 {
  font-size: 44px;
}

.text-48 {
  font-size: 48px;
}

/* prettier-ignore */
.font-20 {
  /* prettier-ignore */
  font-size: 20PX;
}

/* prettier-ignore */
.font-15 {
  /* prettier-ignore */
  font-size: 15PX;
}

/* prettier-ignore */
.font-13 {
  /* prettier-ignore */
  font-size: 13PX;
}

/* prettier-ignore */
.font-10 {
  /* prettier-ignore */
  font-size: 10PX;
}

.font-38 {
  font-size: 38px;
}

.font-26 {
  font-size: 26px;
}

// ======================== 字体颜色 COLOR ===================================
.color-white {
  color: #fff;
}

.color-999 {
  color: #999999;
}

.color-666 {
  color: #666666;
}

.color-b6 {
  color: #b6b6b6;
}

.color-4a {
  color: #4a4a4a;
}

.color-0d {
  color: #0d0d0d;
}

.color-fd6d05 {
  color: #fd6d05;
}

.text-black {
  color: black;
}

.text-red {
  color: #f54a27;
}

.text-brown {
  color: #513522;
}

.text-grey-ccc {
  color: #ccc;
}

.color-969799 {
  color: #969799;
}

.color-black {
  color: #000;
}

.color-lightblack {
  color: #333;
}

.color-lightgray {
  color: #999;
}

.color-gray {
  color: #666;
}

// .color-999 {
//   color: var(--gray-color-999);
// }

.color-orange {
  color: #f24839;
}

.color-5c5c5c {
  color: #5c5c5c;
}

.color-bdbdbd {
  color: #bdbdbd;
}

.color-ccc {
  color: #cccccc;
}

// ======================== 字体位置 TEXT-ALIGN ===================================
.text-right {
  text-align: right;
}

.text-center {
  text-align: center;
}

// ======================== 行高度 LINE-HEIGHT ===================================
.lh-1 {
  line-height: 1;
}

.lh-120,
.leading-120 {
  line-height: 120%;
}

.line-height-1 {
  line-height: 1;
}

.leading-150 {
  line-height: 150%;
}

.leading-127 {
  line-height: 127%;
}

// ======================== 字体粗细 FONT-WEIGHT ===================================
.fw-500 {
  font-weight: 500;
}

.fw-400 {
  font-weight: 400;
}

.bold {
  font-weight: bolder;
}

// ======================== 高度 宽度 WIDTH HEIGHT ===================================
.h-full-100 {
  height: 100%;
}

.w-full-100 {
  width: 100%;
}

.w-full {
  width: 100vw;
}

.w-f100 {
  width: 100%;
}

.w-auto {
  width: auto !important;
}

.h-screen {
  height: 100vh;
}

.h-full {
  height: 100%;
}

.price {
  font-family: var(--font-family-lato);

  &::before {
    content: '¥';
    font-family: var(--font-family-default);
    font-weight: 400;
    font-size: var(--price-prefix-font-size, 75%);
  }
}

// TODO: move to home.less
.price {
  &-current {
    font-family: var(--font-family-lato);
    color: var(--current-price-color, #000);
    display: inline-flex;
    align-items: baseline;
    gap: 4px;

    &__currency {
      display: inline-flex;
      vertical-align: baseline;
      font-family: var(--font-family-default);
      font-size: var(--current-price-currency-font-size, 22px);
      line-height: var(--current-price-currency-line-height, 1);
      font-weight: var(--current-price-font-weight, 400);
    }

    &__amount {
      vertical-align: baseline;
      font-size: var(--current-price-amount-font-size, 32px);
      line-height: var(--current-price-amount-line-height, 1);
      font-weight: var(--current-price-amount-font-weight, 400);
      display: inline-flex;
      // transform: translateY(1px);
    }
  }

  &-line {
    position: relative;
    padding: 0 4px 0 0;
    font-family: var(--font-family-lato);
    font-size: var(--line-price-font-size, 22px);
    font-weight: var(--line-price-font-weight, 300);
    color: var(--line-price-color, #999);
    line-height: 0.9;
    display: inline-flex;
    gap: 2px;

    &::after {
      content: ' ';
      display: block;
      position: absolute;
      left: 50%;
      top: 50%;
      width: 100%;
      border-top: 1px solid #999;
      transform: translate(-50%, -50%);
    }
  }
}

// ======================== 分割线 HR===================================
.hr-ef {
  height: 1px;
  background: #efefef;
  widht: 100%;
}

// ======================== 圆角 BORDER-RADIUS ===================================
.rounded-10 {
  border-radius: 10px;
}

.rounded-12 {
  border-radius: 12px;
}

.rounded-16 {
  border-radius: 16px;
}

.rounded-45 {
  border-radius: 45px;
}

.rounded-20 {
  border-radius: 20px;
}

.rounded-t-20 {
  border-radius: 20px 20px 0 0;
}

.rounded-b-12 {
  border-radius: 0 0 12px 12px;
}

// ======================== OVERFLOW ===================================
.overflow-y-auto {
  overflow-y: auto;
}

.overflow-hidden {
  overflow: hidden;
}

.scroll-y {
  overflow-y: auto;
}

.scroll-x {
  overflow-x: auto;
}

// ======================== Z-INDEX ===================================
.z-index-100,
.z-100 {
  z-index: 100;
}

.z-99 {
  z-index: 99;
}

.z-1 {
  z-index: 1;
}

.z-999 {
  z-index: 999;
}

// ======================== 其他 ===================================
.events-none {
  pointer-events: none;
}

//按钮热区放大
.hotspot {
  cursor: pointer;
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 999px;
  background: transparent;
  transform-origin: 50% 50%;
  transform: translate(-50%, -50%) scale(1.5);
  -webkit-tap-highlight-color: transparent;
}

.c-nowrap {
  white-space: nowrap;
}

.unvisible {
  opacity: 0;
}

.mask {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}

.ellipsis,
.ellipsis-2,
.ellipsis-3 {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;

  &.flex-item {
    display: block;
    width: 1px;
  }

  &.ellipsis-2,
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    white-space: normal;
    word-break: break-all;
    -webkit-line-clamp: 2;
  }

  &.ellipsis-3 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    white-space: normal;
    word-break: break-all;
    -webkit-line-clamp: 3;
  }
}

.hidden-holder {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 0;
  border: none;
  border-radius: 0;
  background: transparent !important;
  color: transparent !important;
  z-index: 99;

  &:after,
  &:before {
    display: none !important;
    border: none !important;
  }
}

.line-through {
  text-decoration: line-through;
}

.text-underline {
  text-decoration: underline;
}

.line-clamp-2 {
  display: block;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  overflow: hidden;
  // white-space: nowrap;
  // line-clamp: 2;
}
.text-overflow-ellipsis {
  text-overflow: ellipsis;
  white-space: nowrap;
}

.must-order-tag {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ff6a16;
  border: 1px solid #ff6a16;
  border-radius: 1000px;
  padding: 0 10px;
  font-size: 20px;
  margin-right: 10px;
  flex-shrink: 0;
  height: 30px;
}

.brand-act-tag {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ff6a16;
  background: #fdf0e9;
  border-radius: 8px;
  padding: 0 10px;
  font-size: 20px;
  margin-right: 10px;
  flex-shrink: 0;
  height: 30px;
}

.campus-tag {
  color: rgba(112, 211, 242, 1);
  border: 1px solid rgba(112, 211, 242, 1);
  border-radius: 8px;
  padding: 4px 10px;
  font-size: 20px;
  margin-right: 10px;
  flex-shrink: 0;
  height: 20px;
  line-height: 20px;
}

// gap
.gap-2 {
  gap: 2px;
}

.gap-4 {
  gap: 4px;
}

.gap-8 {
  gap: 8px;
}

.gap-10 {
  gap: 10px;
}

.gap-20 {
  gap: 20px;
}

.gap-30 {
  gap: 30px;
}

.gap-40 {
  gap: 40px;
}

// top
.top-0 {
  top: 0;
}

.bottom-0 {
  bottom: 0;
}

.right-0 {
  right: 0;
}

.grayscale {
  filter: grayscale(100%);
}

.grayscale-0 {
  filter: grayscale(0);
}

.clear {
  clear: both;
}

.translate-x-50-y--50 {
  transform: translate(50%, -50%);
}

.translate-x-0-y--50 {
  transform: translate(30%, -50%);
}

.translate-y-1-2 {
  transform: translateY(50%);
}

.translate-x-1-2 {
  transform: translateX(50%);
}

.-translate-y-1-2 {
  transform: translateY(-50%);
}

.-translate-x-1-2 {
  transform: translateX(-50%);
}

.box-border {
  box-sizing: border-box;
}

.box-content {
  box-sizing: content-box;
}

.block {
  display: block !important;
}

.grid {
  display: grid;
}
.hidden {
  display: none;
}

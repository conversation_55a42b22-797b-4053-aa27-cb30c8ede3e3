
@import (css) "@style/common";

@font-face {
    font-family: 'iconfont'; /* Project id 2336711 */
    src: url('//at.alicdn.com/t/c/font_2336711_logmwukwns.woff2?t=1732246932917') format('woff2'),
      url('//at.alicdn.com/t/c/font_2336711_logmwukwns.woff?t=1732246932917') format('woff'),
      url('//at.alicdn.com/t/c/font_2336711_logmwukwns.ttf?t=1732246932917') format('truetype');
  }



.smart-icon {
    font-family: 'iconfont' !important;
    position: relative;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 30rpx;
  }



.smart-icon-backspace:before {
    content: '\e6a7';
  }

.cell-wrap{
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    padding: 10px 20px;
    box-shadow: 0px 5rpx 5rpx #fbf9f9 ;

    &:empty {
        display: none;
    }
}
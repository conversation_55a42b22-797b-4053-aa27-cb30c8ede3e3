

.info-wrap{

    background-color: #fff;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    color: #000;
    font-size: 30rpx;

    .header{

        width: 300rpx;
        height: 450rpx;
    }

    .red {
        color: red;
       

        &::last-child{
            color: blue;
        }

        &::first-child{
            color: green;
        }
    }
}

.wrap {

    color: red;
    animation: test 1s;
  
   
  }


  @keyframes test {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}


.test {

    color:red;

    &:first-child{
        color:blue;
    }

    &:last-child{
        color:green;
    }
}



Component({

    externalClasses: ['custom-class'],
    properties:{
        title:{
            type:String
        },

        info : {

            type:Object,
            value:{}
        },
        meta : {
            type:Object,
            value:{
               
            }
        }

    },

    lifetimes:{
        attached(){
            console.log(this.container.parentId)
        }
    },

    observers:{
        "info.name,meta.name":function(newVal,oldVal){
            console.log(newVal,oldVal)
        }
    },


    data:{


        lists:[1,2,3],

        ages:"12345"
    },
    methods:{
        onClickTap() {
            console.error("onClickTap info")
            this.triggerEvent("click")
        },

        onTap() {
            console.log("onTap")
            this.triggerEvent("on-custom-event")
        }
    }

})

export const lifinMix = {

    getName() {

        console.log(`getname `)
    },
    onShow(){
        console.log(`here onshow`);
    }
}


export const lifmix =  {

    behaviors:[
         //@ts-ignore
        Behavior(lifinMix)
    ],
    onShow(){
        console.log(`behavior onshow`)

        this.getName()
    },

    onHide(){
        console.log(`behavior onhide`)
    }
}


export const infomix = {

    behaviors:[
        //@ts-ignore
        Behavior(infoinmix)
    ],
    methods:{


        getInfo(){
            console.log(`getinfo`)
        }
    }
}

export const infoinmix = {

    attached(){
        console.log(`comp attached`)
    }
}


<view class = "complete-wrap">

    <view class = "title" style = "{{buttonStyle}}">button:</view>

    <button>按钮</button>


<view class = "title">icon:</view>

    <icon type = "success" size = "32" color = "red" />



<view class = "title">progress:</view>

<view class="progress-box">
  <progress percent="80" color="#10AEFF" active stroke-width="3" />
</view>


<view class = "title">rich-text:</view>
 <rich-text nodes="{{nodes}}"></rich-text>



<view class = "title">checkbox:</view>

  <checkbox-group bindchange="checkboxChange">
          <label  wx:for="{{items}}" wx:key="{{item.value}}">
            <view >
              <checkbox value="{{item.value}}" checked="{{item.checked}}"/>
            </view>
            <view >{{item.name}}</view>
          </label>
        </checkbox-group>

<view class = "title">input:</view>

         <input  auto-focus placeholder="将会获取焦点" class="inp"/>
   
</view>
.info-custom-class{
  color: red;
}

.userinfo {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #aaa;
}

.userinfo-avatar {
  overflow: hidden;
  width: 128rpx;
  height: 128rpx;
  margin: 20rpx;
  border-radius: 50%;
}

.usermotto {
  margin-top: 200px;
}

.container {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  box-sizing: border-box;
  background-color: #f7f7f7;
}

.section {
  width: 100%;
  margin-bottom: 30rpx;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: bold;
}

.swiper {
  width: 100%;
  height: 400rpx;
  border-radius: 8rpx;
  overflow: hidden;

  &.vertical {
    height: 600rpx;
  }
}

.swiper-item {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;

  // 基础轮播图颜色
  &-1 { background-color: #1aad19; }
  &-2 { background-color: #3cc51f; }
  &-3 { background-color: #07c160; }

  // 垂直轮播图颜色
  &-4 { background-color: #ff6b6b; }
  &-5 { background-color: #4ecdc4; }
  &-6 { background-color: #45b7d1; }

  // 自定义指示点颜色
  &-7 { background-color: #2c3e50; }
  &-8 { background-color: #34495e; }
  &-9 { background-color: #7f8c8d; }
}

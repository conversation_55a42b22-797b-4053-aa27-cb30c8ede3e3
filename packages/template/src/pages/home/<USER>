import _ from "lodash"

let meta = {
   name:"123"
}
const url = "https://images.wosaimg.com/f2/7342fd389b422f478cc528b0866cfa239b3764.png?x-oss-process=image/resize,l_750"
Page({

   data:{
      nodes:[{
         name: 'div',
         attrs: {
           class: 'wrapper abc',
           style: 'color: orange;',
         },
         children: [{
           type: 'text',
           text: 'Hello&nbsp;World!',
         }, {
           name: 'span',
           attrs: {
             style: 'color: orange; font-size: 40px',
           },
           children: [{
             type: 'text',
             text: 'leon!'
           }]
         }],
       }],
      info:{
         name:"123"
      },
      meta:{
         name:"123"
      },

      processedListsL:[
       
      ],
      url,
      current:0,

     
   },

   onReady(){

      setTimeout(() => {
         let { processedListsL } = this.data;
         processedListsL.push(url)
         this.setData({
            processedListsL : [

               "https://images.wosaimg.com/f2/7342fd389b422f478cc528b0866cfa239b3764.png?x-oss-process=image/resize,l_750"
               ,        "https://images.wosaimg.com/f2/7342fd389b422f478cc528b0866cfa239b3764.png?x-oss-process=image/resize,l_750"
               ,        "https://images.wosaimg.com/f2/7342fd389b422f478cc528b0866cfa239b3764.png?x-oss-process=image/resize,l_750"
               ,        "https://images.wosaimg.com/f2/7342fd389b422f478cc528b0866cfa239b3764.png?x-oss-process=image/resize,l_750"
               
            ]
              
         })
      }, 1000);
     
   },
   onClickTap() {

      console.error(`onClickTap home`)

     
   },
   onTap(e){
      console.error("onTap",e)
     console.error("onTap")
   },

   onSwiperChange(e) {

      console.error("e is ",e)
   }
})
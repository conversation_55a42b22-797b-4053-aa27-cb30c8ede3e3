

Page({

    
    getSystemInfo(){

        let res = wx.getSystemInfoSync();

       this.showResult(JSON.stringify(res))
    },


    showResult(msg) {

        wx.showModal({
            title:"结果",
            content:msg
        })
    },


    navigateBack(){
        wx.navigateBack()
    },

    showToast(){
        wx.showToast({
            title: '成功',
            icon: 'none',
            duration: 2000
          })
          
    },

    showLoading(){
        wx.showLoading({
            title:"加载中..."
        })
    }
}) 
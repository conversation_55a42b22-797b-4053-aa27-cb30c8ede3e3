




<view class = "introduce-wrap">

    <view class = "title">Concise是什么</view>

    <view>

        <view style="margin-bottom:10px">Concise 是智慧经营开发的一款基于微信小程序 DSL 的，可扩展的多端研发框架。使用微信小程序原生 DSL 构建，使用者只需书写一套微信小程序，就可以通过 Concise 的转端编译能力，
        将源码分别编译出可以在不同端（微信/支付宝/Web…）运行的产物。</view>

        <view>Concise 以多端编译为基础，配以面向全生命周期的插件体系，覆盖从源码到构建产物的每个阶段，支持各类功能扩展和业务需求，无论是基础的页面和组件还是复杂的分包和插件，
        Concise 都可以胜任，帮助你高效地开发多端小程序和H5。</view>
    </view>


    <view catchtap = "onHandleRedirect" class = "test">测试redireTo</view>

</view>
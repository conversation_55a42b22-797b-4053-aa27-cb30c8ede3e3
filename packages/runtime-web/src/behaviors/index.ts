





import {
    SetData
} from "./setData"

import {
    CreateSelectorQuery
} from "./createSelectorQuery"

import {
    EventChannel
} from "./channel"


export * from "./relations"



export const baseInstanceCreators = [

   // EmitBehavior,
    SetData,
    CreateSelectorQuery

]


export const componentInstanceCreators = [
   // ExternalClassesBehavior
]

export const pageInstanceCreators = [
    // EventChannel,
]
import { EventEmitter } from "../event";



export  const EventChannel = (base) =>  {

    if (!base) base = class {}

    return  class  extends base {

        // _routerChannel:EventEmitter
    
    
        // setChannel(channel : EventEmitter) {
        //     this._routerChannel = channel
        // }
    
    
        // getOpenerEventChannel(){
        //     return this._routerChannel;
        // }
    
    
        // disconnectedCallback() {
        //     //@ts-ignore
        //     super.disconnectedCallback && super.disconnectedCallback()

    
        //     if (this._routerChannel) {
        //         this._routerChannel.clear();
        //     }
        // }
    
    }
}
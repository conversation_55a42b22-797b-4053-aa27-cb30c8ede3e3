




import {
  sqb
} from "../apis/index"

export const  CreateSelectorQuery = (base) => {
  return class extends base {

    createSelectorQuery() {

      //@ts-ignore
        return sqb.createSelectorQuery(this.container);
    }

    selectComponent = (match) => {

        const selector = this.createSelectorQuery().select(match)

        //@ts-ignore
        return selector.target ? selector.target.instance : null;
    }


    createIntersectionObserver = (...args) => {

      return sqb.createIntersectionObserver(...args)
    }

}
} 



import {
  setNestedValue
} from "../helper"

export const  SetData = (base) => {

  return class extends base {

    constructor() {
        super();
 
    }


  // 这里是lit 更新过来的，肯定是组件
  _concise_update_data(values) {

    values.forEach(({property,value}) => {
      this.data[property] = value;

      if (this.properties.hasOwnProperty(property)) {
        this.properties[property] = value;
      }
    })


  }

    // 自定义的 setData 方法，支持回调函数
  setData(newData:any, callback:Function) {
    Object.entries(newData).forEach(([path, value]) => {
      setNestedValue(this.data, path, value);
     
    });

    // 组件没挂在到DOM，就setData了，这里防止下
    this.container && this.container.setData(newData,callback);
  }



  
}

}



import {
    findRelationTargetByPath,
    execChildRelations,
    execSelfRelations
} from "../relations"


export const  RelationsBehavior = (base) => {

    return class extends base {



        getRelationNodes(path) {


            //@ts-ignore
            const item = this.concise_relations[path]

            if (!item) return []

            const { type } = item;

            return findRelationTargetByPath({
                instance:this,
                relPath:path,
                isParent:type === "parent" || type === "ancestor" ? true:false
            }).map((item) => item.instance)

        }

        execChildRelations(params) {
            execChildRelations.call(this,{
                 //@ts-ignore
                relations:this.concise_relations,
                name:params.name,
                behaviorId:params.behaviorId,
                target:params.target
            })
        }
        

        attached() {
            super.attached && super.attached();
            execSelfRelations.call(this,{
                name:"linked",
                 //@ts-ignore
                relations:this.concise_relations
            });


        }

        detached() {
            super.detached && super.detached();
          
            execSelfRelations.call(this,{
                name:"unlinked",
                relations:this.concise_relations
            });
        }


    }


}
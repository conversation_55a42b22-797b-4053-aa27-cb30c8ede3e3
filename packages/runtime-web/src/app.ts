import {
    LitElement, html, css,
    unsafeCSS
} from "lit";
import { XData } from "@wosai/smart-mp-concise-runtime-base"

import {
    rootComponentName
} from "./helper"
import { isFunction } from "@wosai/smart-mp-concise-shared";
import { RouterElement } from "./routerElement";
export type IParams = {

    pages: XData,
    json: XData,
    styles
}


export function runApp(params: IParams) {

    //@ts-ignore
    const options = window.$$concise_app_options$$;

    const { pages,} = params;


    const AppComponent = class extends RouterElement {

        static styles = css`
                ${super.styles}
         `;
        constructor() {
            super();
            this.initOptions();

            this.initRoutes();

        }


        initOptions() {
            for (const key in options) {
                const value = options[key];
                if (isFunction(value)) {
                    this[key] = value.bind(this)
                } else {
                    this[key] = value
                }
            }
        }

        initRoutes() {
            let routes = []
            for (const key in pages) {
                routes.push({
                    name: key,
                    url: pages[key]
                })

            }
            //@ts-ignore
            this.routes = routes

        }

        connectedCallback(): void {
            super.connectedCallback && super.connectedCallback();
            
           
        }

        


        firstUpdated() {

            if (options.onLaunch) {
                options.onLaunch.call(this)
            }

            if (!(this.getAllPages().length)) {
                this.start();
            }
        }





        protected render() {

            return html``
        }

    }

    customElements.define(rootComponentName, AppComponent);
}

import { generateId } from "@wosai/smart-mp-concise-shared"
import { normalizeEventName } from "./helper"


export class BaseInstance {

    concise_methods = {}
    properties = {}
    data = {}
    concise_relations = {}

    concise_externalClasses = []


    route?: string
    options?: Record<any, any>
    __wxExparserNodeId__: string
    container: any

    get shadowRoot() {
        //@ts-ignore
        return this.container.shadowRoot
    }

    constructor() {

        this.__wxExparserNodeId__ = generateId();
    }


    triggerEvent(name,args) {
        const eventName = normalizeEventName(name);
        //@ts-ignore
        this.container.dispatchEvent(new CustomEvent(eventName, {
            detail: args,
            bubbles: false, 
            composed: false 
          }));
        
    }




}
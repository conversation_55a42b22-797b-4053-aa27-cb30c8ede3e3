
import {
    // showModal,
    // showLoading,
    // showToast,
    // hideToast,
    // hideLoading,
    elementQuery,
    // getMenuButtonBoundingClientRect,
    // hideHomeButton
} from "./ui"

// import {
//     getSystemInfo,
//     getSystemInfoSync,
//     getAccountInfoSync,
//     getLaunchOptionsSync,
//     onError,
//     onAppShow,
//     onAppHide,
//     onUnhandledRejection,
//     getExtConfigSync,
//     login,
//     setNavigationBarColor,
//     nextTick,
//     scanCode,
//     makePhoneCall,
//     hideShareMenu
// } from "./basic"


// import {
//     request,
//     getBackgroundFetchData
// } from "./network"

// import {
//     getStorage,setStorage,
//     getStorageSync,setStorageSync,
//     removeStorageSync,
//     removeStorage
// } from "./storage"


// import {
//     navigateBack,
//     navigateTo,
//     redirectTo,
//     reLaunch
// } from "./router"



const wrapApi = (func:Function) => {

    return  function (params:any = {}) {
          const { success = () => {},fail = () => {},complete = () => {} } = params
          let res = func(params);

          if (res instanceof Promise) {

            res.then((data) => {
              success(data);
              complete(data)
            },(err) => {
              fail(err)
            })
            
          }else {
            success(res);
            complete(res)

          }

          return res;

    }

} 


let  sqb = {
    // makePhoneCall:wrapApi(makePhoneCall),
    // getBackgroundFetchData,
    // showModal,
    // showToast,
    // hideToast,
    // request,
    // getStorage:wrapApi(getStorage),
    // setStorage,
    // getStorageSync,setStorageSync,
    // removeStorageSync,
    // onUnhandledRejection,
    // removeStorage,
    // showLoading,
    // hideLoading,
    // getSystemInfo,
    // getSystemInfoSync,
    // navigateTo,
    // navigateBack,
    // reLaunch,
    // redirectTo,
    createSelectorQuery:elementQuery.createSelectorQuery,
    createIntersectionObserver:elementQuery.createIntersectionObserver,
    // getMenuButtonBoundingClientRect,
    // getAccountInfoSync,
    // getLaunchOptionsSync,
    // onError,
    // onAppHide,
    // onAppShow,
    // hideHomeButton,
    // getExtConfigSync,
    // login : wrapApi(login),
    // setNavigationBarColor,
    // nextTick,
    // scanCode:wrapApi(scanCode),
    // hideShareMenu
}







export {
    sqb
}


// 支持端API
export function insertMockPlatformApi(config) {

    if (!config) return ;

    Object.keys(config).forEach((key) => {
        sqb[key] = wrapApi(config[key])
    })

}


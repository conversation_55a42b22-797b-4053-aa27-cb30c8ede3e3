import axios from 'axios';

export function request(options) {
  let {
    url,
    method = 'GET',
    data = {},
    header = {},
    dataType = 'json',
    responseType = 'json',
    timeout = 6000, // 超时参数
    success = () => {},
    fail = () => {},
    complete = () => {},
  } = options;


  // 创建 axios 请求配置
  const config:any = {
    url,
    method: method.toUpperCase(),
    headers: { ...header },
    timeout,
    responseType, // axios 中的 responseType 支持 'text', 'json' 等
  };

  // 如果是 GET 请求，将 data 作为查询参数处理
  if (config.method === 'GET') {
    config.params = data;
  } else {
    // POST 请求时，发送 JSON 格式数据
    config.data = data;
  }

  return new Promise((resolve, reject) => {
  // 发送请求
  axios(config)
    .then(response => {
   //   const result = dataType === 'json' ? response.data : response.data.toString();

   const result = {
    data: response.data, // 服务器返回的数据
    statusCode: response.status, // HTTP 状态码
    header: response.headers, // HTTP 响应头
    cookies: response.headers['set-cookie'] || [], // 获取 cookies
    profile: {}, // 模拟返回调试信息（此处暂时为空）
    exception: null, // 异常信息为空
  };
      success(result); // 请求成功时回调
      resolve(result)
    })
    .catch(error => {
 
      const errorInfo = {
        errMsg: `request:fail ${error.message}`,
        statusCode: error.response ? error.response.status : 0, // 捕获错误状态码
      };

      fail(errorInfo);
      reject(errorInfo)
    })
    .finally(() => {
      complete(); // 请求完成时回调
    });

  })
}

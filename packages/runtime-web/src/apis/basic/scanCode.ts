import { isMobile } from "../../components/utils";

import { Html5Qrcode } from "html5-qrcode"

let fileInput;

function chooseImageFile() {

   return new Promise((resolve,reject) => {

    if (!fileInput) {
        fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = 'image/*';
         // 创建一个隐藏的 div 元素，用于 Html5Qrcode
         let hiddenDiv = document.createElement('div');
         hiddenDiv.id = 'hidden-result-' + Date.now(); // 给 div 一个唯一的 id
         hiddenDiv.style.display = 'none'; // 将它隐藏
         document.body.appendChild(hiddenDiv); // 动态添加到 body 中

        // Add an event listener to handle the file selection
        fileInput.addEventListener('change', function(event) {
            const file = event.target.files[0];
            if (file) {
                const html5QrCode = new Html5Qrcode(hiddenDiv.id);

                html5QrCode.scanFile(file, true)
                    .then((qrCodeMessage) => {
                        resolve(qrCodeMessage)
                       
                    })
                    .catch(err => {
                        reject(err)
                       
                    });
            }
        });
    }
   })
    


}


function chooseQrCode(params:any = {}) {

    const {onlyFromCamera  } = params;


    if (onlyFromCamera) {

    }

}

export async function scanCode(params) {
  
    if (isMobile()) {



    }else {
      let msg = await chooseImageFile();
      return {
        result:msg
      }
    }
   
  }
  
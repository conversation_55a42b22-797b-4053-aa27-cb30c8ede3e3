import { isMobile } from "../../components/utils";

export function getSystemInfo(
  params: any = {}
) {
  const { success = ()=>{}, fail = ()=>{}, complete = () => {} } = params
  return new Promise((resolve, reject) => {
    try {

      // 模拟获取系统信息
      const systemInfo = getSystemBase()

      // 调用成功回调
      if (typeof success === 'function') {
        success(systemInfo);
      }
      resolve(systemInfo);
    } catch (error) {
      // 调用失败回调
      if (typeof fail === 'function') {
        fail(error);
      }
      reject(error)

    } finally {
      complete();
    }
  });

}


export function getSystemInfoSync() {

  return getSystemBase();
}


export function hideShareMenu() {
  return 
}

function getSystemBase() {

  try {
    // 模拟获取系统信息
    const systemInfo = {
      model: navigator.userAgent, // 设备型号（模拟）
      system: navigator.platform, // 操作系统（模拟）
      screenWidth: window.screen.width, // 屏幕宽度
      screenHeight: window.screen.height, // 屏幕高度
      windowWidth: window.innerWidth, // 浏览器视口宽度
      windowHeight: window.innerHeight, // 浏览器视口高度
      language: navigator.language, // 浏览器语言
      version: navigator.appVersion, // 浏览器版本（模拟）
      fontSizeSetting: parseFloat(getComputedStyle(document.documentElement).fontSize), // 字体大小（模拟）
      statusBarHeight: 20, // 状态栏高度（模拟，实际可能需要特定环境支持）
      pixelRatio: window.devicePixelRatio, // 像素比
      SDKVersion: "2.3.4"
    };

    return systemInfo
  } catch (error) {
    // 如果发生错误，可以选择抛出异常或者返回一个默认值
    console.error('Failed to get system info:', error);
    return null;
  }

}


export function getAccountInfoSync() {

  return {

    miniProgram: {
      appId: "wxd2f16468474f61b8",
      envVersion: "develop",
      version: "7.8.0"
    }
  }
}


export function getLaunchOptionsSync() {

  return {
    scene: 1000
  }
}


export function onError(params: any) {


}


export function onAppShow(params: any) {

}




export function onAppHide(params: any) {

}


export function onUnhandledRejection(params: any) {


}


export async function getBackgroundFetchData(params: any) {

}


export function getExtConfigSync() {

}


export async function login(params){

  return {
    //@ts-ignore
    code: "0f1tkjGa1Sn3dI0n8DHa1Zwc8t4tkjGn",
    errMsg: "login:ok"    
  }
}


export function setNavigationBarColor(){

  
}


export function nextTick(callback:any) {
  return Promise.resolve().then(callback);
}



export function makePhoneCall(params :any = {}) {
  if (!params.phoneNumber) return ;
  if (isMobile()) {
    window.location.href = `tel:${params.phoneNumber}`
  }else {
    console.warn(`web 浏览器不支持makePhoneCall接口，请使用手机浏览器`)
  }
}



// 同步设置存储
export function setStorageSync(key, value) {
    if (value === undefined) return ;
    try {
      const jsonValue = JSON.stringify(value);
      localStorage.setItem(key, jsonValue);
    } catch (error) {
      console.error('setStorageSync:fail', error);
    }
  }
  
  // 同步获取存储
  export function getStorageSync(key) {
    try {
      const jsonValue = localStorage.getItem(key);
      return jsonValue ? JSON.parse(jsonValue) : null;
    } catch (error) {
      console.error('getStorageSync:fail', error);
      return null;
    }
  }
  
  // 同步移除存储
  export function removeStorageSync(key) {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error('removeStorageSync:fail', error);
    }
  }

  // 异步设置存储
export function setStorage(params:any = {}) {

    const { key, data, success = () => {}, fail = (params) => {}, complete = () => {} } = params;
    return new Promise((resolve, reject) => {
      try {
        if (data !== undefined) {
          const jsonValue = JSON.stringify(data);
          localStorage.setItem(key, jsonValue);
        }
       
        success();
        resolve(null);
      } catch (error) {
        console.error('setStorage:fail', error);
        fail(error);
        reject(error);
      } finally {
        complete();
      }
    });
  }
  
  // 异步获取存储
  export async function getStorage(params:any = {}) {
    const jsonValue = localStorage.getItem(params.key);
        const data = jsonValue ? JSON.parse(jsonValue) : null;
        return {data}
  }
  
  // 异步移除存储
  export function removeStorage(params:any = {}) {

    const { key, data, success = () => {}, fail = (params) => {}, complete = () => {} } = params;
    return new Promise((resolve, reject) => {
      try {
        localStorage.removeItem(key);
        success();
        resolve(null);
      } catch (error) {
        console.error('removeStorage:fail', error);
        fail(error);
        reject(error);
      } finally {
        complete();
      }
    });
  }
  


// 节点相关查询接口
import { getRootView, updateRootView, wait } from '../../utils'
import { isFunction, isObject, } from '@wosai/smart-mp-concise-shared'
import IntersectionObserver from './intersection-observer'
import { FIELD_CONFIG_METHODS_MAP, mapTarget } from './utils'
import { getElementOrShadowRoot } from '../../../helper'

export default {
  createIntersectionObserver(options?) {
    return new IntersectionObserver(options)
  },
  createSelectorQuery(root?: HTMLElement) {
    return new SelectorQuery(root)
  },
  updateRootView
}
class SelectorQuery {
  target: HTMLElement | NodeListOf<HTMLElement>

  execPromises: Promise<any>[] = []
  fieldsPromise: Promise<any>[] = []
  root?: HTMLElement
  constructor(root?: HTMLElement) {
    this.root = root
  }


  in(instance) {
    this.root = instance.container;
    return this;
  }
  getRootView() {
    return this.root || getRootView();
  }

  
  select(selector: string) {
   
    this.target =  getElementOrShadowRoot(this.getRootView()).querySelector(selector) as HTMLElement
    return this
  }

  selectAll(selector: string) {
    this.target =  getElementOrShadowRoot(this.getRootView()).querySelectorAll(selector)
    return this
  }

  selectViewport() {
    //todo
  }

  boundingClientRect() {
    const target = this.target
    this.execPromises.push(
      wait().then(() => mapTarget(target, FIELD_CONFIG_METHODS_MAP.getBoundingClientRect))
    )

    return this
  }

  scrollOffset() {
    const target = this.target
    this.execPromises.push(
      wait().then(() =>
        mapTarget(target, FIELD_CONFIG_METHODS_MAP.scrollOffset)
      )
    )

    return this
  }

  fields(config, callback) {
    const target = this.target

    this.fieldsPromise.push(
      wait()
        .then(() => {
          return mapTarget(target, (el: HTMLElement) => {
            return Object.keys(config).reduce((res, key) => {
              if (
                config[key] &&
                typeof FIELD_CONFIG_METHODS_MAP[key] === 'function'
              ) {
                const value = FIELD_CONFIG_METHODS_MAP[key](el, config[key])

                if (isObject(value)) res = { ...res, ...value }
                else res[key] = value
              }
              return res
            }, {})
          })
        })
        .then((res) => isFunction(callback) && callback(res))
    )

    return this
  }

  exec(callback) {
    Promise.all(this.execPromises).then((res) => {
      isFunction(callback) && callback(res)
    })

    if (this.fieldsPromise.length > 0) {
      Promise.all(this.fieldsPromise)
    }
  }
}

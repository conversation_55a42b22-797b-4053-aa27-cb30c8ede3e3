//@ts-nocheck
class ModalManager {
  constructor() {
    this.modalElement = document.createElement('concise-modal-element');
    document.body.appendChild(this.modalElement);
  }

  showModal({ title, content, confirmText, cancelText }) {
    return new Promise((resolve, reject) => {
      this.modalElement.show({ title, content, confirmText, cancelText });
      this.modalElement.addEventListener('confirm', () => resolve(true), { once: true });
      this.modalElement.addEventListener('cancel', () => resolve(false), { once: true });
    });
  }
}



let  modalManager :any

export function showModal(options) {

  if (!modalManager) modalManager = new ModalManager();

  return modalManager.showModal(options);
}
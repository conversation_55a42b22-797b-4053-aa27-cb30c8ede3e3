
//@ts-nocheck
import type {
  LoadingElement
} from "../../components/other/loading"

let loadingElement: LoadingElement;

export function showLoading({ title = 'Loading...', mask = true } = {}) {

  if (!loadingElement) {
    loadingElement = document.createElement('concise-loading-element');
    document.body.appendChild(loadingElement);
  }
  loadingElement.title = title;
  loadingElement.mask = mask;
  loadingElement.active = true;
}

export function hideLoading() {
  if (loadingElement)
    loadingElement.active = false;
}
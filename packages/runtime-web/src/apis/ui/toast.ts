
//@ts-nocheck

class ToastManager {
    constructor() {
      this.toastElement = document.createElement('toast-element');
      document.body.appendChild(this.toastElement);
    }
  
    showToast({ title, icon = 'none', duration = 1500 }) {
      this.toastElement.show(title, icon, duration);
    }

    hideToast() {
      this.toastElement.hide();
    }
  }
  
  let toastManager :any
  
  export function showToast(options) {
    if (!toastManager) toastManager= new ToastManager();
    toastManager.showToast(options);
  }

  export function hideToast() {
    if (toastManager) toastManager.hideToast()
  }
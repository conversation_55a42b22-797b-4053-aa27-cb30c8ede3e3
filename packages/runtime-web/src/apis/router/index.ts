
import{
    eventManager,
    EventEmitter
} from "../../event"



export async function navigateTo(params:any) {
         await eventManager.emit("navigateTo",params);

       const { success, events } = params
      
       if (!success && !events) return ;

    //    const channel = new EventEmitter();

    //    if (success) {
    //         params.success({
    //             channel
    //         })
    //    }

    //    if (events) {
    //         const names = Object.keys(events);
    //         names.forEach((name) => {
    //             const func = events[name];
    //             channel.on(name,func)
    //         })

    //    }

}


export function navigateBack(params){

    eventManager.emit("navigateBack",params);
}


export function redirectTo(params) {
    eventManager.emit("redirectTo",params);
}


export function reLaunch(params){

    // todo 
}
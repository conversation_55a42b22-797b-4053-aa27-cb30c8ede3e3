

export type IRelationsParams = {

    instance:any, // 当前节点
    target:any,// 目标节点
    path?:string,  // 以路径关联
    behaviorId?:string  // 以behavior关联
    name:string ,// 执行的函数名称
    relations:Object ,// 业务传的relations
}

import _ from "lodash"


import {
    getAbsPath,
    findAncestors,
    findSlotDescendants
} from "./helper"









export type IWalkOptions = {
    processBehaviorLinkItem?:Function,
    processRelativeLinkItem?:Function,
    instance:any
}



  // 加载的时候执行child relations
 export function  execSelfRelations({
    name,
    relations,
 }) {

    if (!relations) return;

    const self = this;
    const callParentElementRelation = ({
        parent,
        behaviorId,
        child
    }) => {
        if (!parent) return;
        let params: any = { name, target: child };
        if (behaviorId) params.behaviorId = behaviorId;

        parent.instance.execChildRelations(params)
    }

    walkRelations(relations, {
        instance:self,
        processBehaviorLinkItem({
            item
        }) {

                if (item.type !== "parent" && item.type !== "ancestor") return ;
                let behaviorId = item.target.concise_behavior_id
                let parent = findRelationTargetByBehaviorId({
                    instance: self,
                    behaviorId: behaviorId
                })

                if (item[name]) item[name].call(self,parent);
                callParentElementRelation({ parent, behaviorId, child: self })
            
        },

        processRelativeLinkItem({
            item, key
        }) {
            if (item.type !== "parent" && item.type !== "ancestor") return ;

            const parent = findRelationTargetByPath({
                instance: self,
                relPath: key,
                isParent:true
            })[0];
            if (item[name]) item[name].call(self,parent);

            callParentElementRelation({ parent, behaviorId: null, child: self })
        }
    })


}


export function execChildRelations({
    relations,
    name,
    behaviorId,
    target
}) {

    let results = findRelationItemsByRelativePathOrBehaviorId({
        instance: this,
        relations,
        behaviorId,
        targetInstance: target
    })


    results.forEach((item) => {

        if (item[name]) item[name].call(this, target)
    })

}



export function walkRelations(relations,options:IWalkOptions) {

    if (!relations) return ;

    const { instance } = options
    for (const key in relations) {
        const  item  = relations[key];
        if (relationItemIsBehaviorLink({instance,item})) {
            options.processBehaviorLinkItem && options.processBehaviorLinkItem({
                item,key
            })
        }else if (relationItemIsRelativeLink({instance,item,key})) {
            options.processRelativeLinkItem && options.processRelativeLinkItem({
                item,key
            })
        }

    }

}

 // 根据路径找到目标文件
 export const findRelationTargetByPath = ({
    instance,
    relPath,
    isParent
 }) => {
    
    const selfUserRequest = instance.container._concise_user_info.componentPath;

    const targetAbsPath = getAbsPath(selfUserRequest,relPath);



    let findFunction = isParent ? findAncestors : findSlotDescendants

    let target = findFunction(instance.container,(targetElement) => {

            return  targetElement._concise_user_info && targetElement._concise_user_info.componentPath === targetAbsPath
         });
   
    

    return target;

}



// 父组件找对应的relation项
export function findRelationItemsByRelativePathOrBehaviorId ({
    instance,
    relations,
    behaviorId,
    targetInstance
}) {

    let results = [];


    walkRelations(relations,{
        instance,
        processBehaviorLinkItem:({item}) => {

            if (behaviorId === item.target.concise_behavior_id ) {
                results.push(item)
            }

        },

        processRelativeLinkItem:({item,key}) => {
            const targetAbsPath = getAbsPath(instance.container._concise_user_info.componentPath,key);
            if (targetAbsPath === targetInstance.container._concise_user_info.componentPath) {
                results.push(item)
            }
    
        }
    })
    
    
    return results

}


export function relationItemIsBehaviorLink({
    instance,
    item,
}) {

    return item.target && item.target.concise_behavior_id && instance.behaviorMap.has(item.target.concise_behavior_id)
}


export function relationItemIsRelativeLink({
    instance,
    item,
    key
}) {

    return !relationItemIsBehaviorLink({instance,item}) && key.startsWith(".")
}


export function findRelationTargetByBehaviorId({
    instance,
    behaviorId
}) {

    return findAncestors(instance,(parentElement) => {
            return parentElement.concise_hasBehaviorId(behaviorId)

    });


}


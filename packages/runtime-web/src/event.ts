import { generateId } from "@wosai/smart-mp-concise-shared";

export class EventEmitter {
    events:any
    id:string
    constructor() {
      this.events = {};
      this.id = generateId()
    }
  
    on(event, listener) {
      if (!this.events[event]) {
        this.events[event] = [];
      }
      this.events[event].push(listener);
    }

    clear() {
        this.events = {}
    }
  
    off(event, listenerToRemove) {
      if (!this.events[event]) return;
  
      this.events[event] = this.events[event].filter(listener => listener !== listenerToRemove);
    }
  
    async emit(event, ...args) {
      if (!this.events[event]) return;
  
      const listeners = this.events[event];
      const results = [];
  
      for (const listener of listeners) {
        results.push(await listener(...args));
      }
  
      return results;
    }
  }



  
export const eventManager = new EventEmitter();
  
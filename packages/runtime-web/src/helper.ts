import {
  SqbComponentOptions
} from "@wosai/smart-mp-concise-runtime-base"
import { isFunction, getTag, isArray, isObject } from "@wosai/smart-mp-concise-shared";
import { BaseElement } from "./baseElement";

import qs from 'qs';
import { convertDataSet } from "./eventConvert";

import { sha256 } from "js-sha256";

// 浏览器原生DOM事件列表
const NATIVE_DOM_EVENTS = new Set([
  // 鼠标事件
  'click', 'dblclick', 'mousedown', 'mouseup', 'mousemove', 'mouseover', 'mouseout', 'mouseenter', 'mouseleave',
  // 键盘事件
  'keydown', 'keyup', 'keypress',
  // 表单事件
  'submit', 'reset', 'change', 'input', 'focus', 'blur', 'select',
  // 文档事件
  'load', 'unload', 'beforeunload', 'resize', 'scroll',
  // 触摸事件
  'touchstart', 'touchend', 'touchmove', 'touchcancel',
  // 拖拽事件
  'drag', 'dragstart', 'dragend', 'dragenter', 'dragover', 'dragleave', 'drop',
  // 剪贴板事件
  'copy', 'cut', 'paste',
  // 媒体事件
  'play', 'pause', 'ended', 'timeupdate', 'volumechange',
  // 其他事件
  'contextmenu', 'wheel', 'error', 'abort', 'beforeprint', 'afterprint'
]);

/**
 * 判断事件名是否为浏览器原生DOM事件
 * @param eventName 事件名
 * @returns 如果是原生事件则添加"concise-"前缀，否则原样返回
 */
export function normalizeEventName(eventName: string): string {
  if (NATIVE_DOM_EVENTS.has(eventName.toLowerCase())) {
    return `concise-${eventName}`;
  }
  return eventName;
}

//export let componentsMap = new Map()
export function composeOptions(options: SqbComponentOptions, result: Array<SqbComponentOptions>) {


  if (!options) return;
  const { behaviors = [] } = options


  behaviors
    .filter((beh) => {
      return typeof beh !== "string"
    })
    .forEach((beh) => {
      composeOptions(beh, result)
    })


  // delete options.behaviors;

  result.push(options);

}




// 合并 mixins 的函数
export function reduceApplyCreator(creators, Base = class { }) {

  return creators.reduce((base, creator, currentIndex) => {
    return creator(base)
  }, Base);
}




export const rootComponentName = "concise-app"



// 浏览器环境获取绝对路径,给relations使用

export function getAbsPath(basePath: string, relativePath: string) {



  // 创建一个 URL 对象，将 basePath 作为基准路径
  const baseUrl = new URL(basePath, 'https://example.com/');

  // 使用 baseUrl 和相对路径创建新的 URL 对象
  const absoluteUrl = new URL(relativePath, baseUrl);

  // 获取绝对路径
  const absolutePath = absoluteUrl.pathname;

  return absolutePath;

}


// 找到第一个匹配的节点
export function findAncestors(element, selector, once = true) {

  let result = [];
  let currentElement = element.parentElement;

  while (currentElement) {
    if (isFunction(selector)) {
      if (selector(currentElement)) {
        result.push(currentElement)
        if (once) break;
      }

    }
    else if (currentElement.matches(selector)) {
      result.push(currentElement)
      if (once) break;
    }



    currentElement = currentElement.parentElement;
  }

  return result;
}



export function findSlotDescendants(element, selector) {

  // slot 元素会放到light DOM中去
  let children = Array.from(element.querySelectorAll("*"));

  return children.filter((child) => {

    if (isFunction(selector)) {
      return selector(child)
    } else {
      // @ts-ignore
      return child.matches(selector)
    }
  })
}





export function getQueryParams(url: string) {
  // 获取 '?' 后面的部分
  let queryString = url.split('?')[1];

  if (!queryString) {
    return {}; // 如果没有查询参数，返回空对象
  }

  queryString = decodeURIComponent(queryString)
  // 使用 qs.parse 将查询字符串解析为对象
  return qs.parse(queryString);
}

// 解析路径为键数组
export function parsePath(path) {
  return path
    .replace(/\[(\d+)\]/g, '.$1') // 处理数组索引
    .split('.') // 按点分隔
    .filter(Boolean); // 去除空值
}

// 递归函数，按路径设置值
export function setNestedValue(obj, path, value) {
  const keys = parsePath(path);
  const lastKey = keys.pop();
  let current = obj;

  // 逐级访问对象
  keys.forEach(key => {
    if (!(key in current)) {
      throw new Error(`set Data path ,key ${key} is not in data`)
    }
    current = current[key];
  });

  // 设置值
  current[lastKey] = value;
}






export const COMPONENT_TYPE_LIT_ELEMENT = 0x01;
export const COMPONENT_TYPE_USER_DEFINE = 0x01 << 1;

function getSelectorComponentsDescriptor(el, selector, isAll = false) {

  let lists :any= [];
  if (isAll) {
    lists = getElementOrShadowRoot(el).querySelectorAll(selector)
  } else {
    lists = [getElementOrShadowRoot(el).querySelector(selector)];
  }


  lists = Array.from(lists)
    .filter(Boolean)
    .filter((item:any) => { return item._concise_component_type & COMPONENT_TYPE_LIT_ELEMENT })
    .map((item) => { return createInstanceDescriptor(item) })



  return isAll ? lists : lists[0];
}


// 用于将 camelCase 转换为 kebab-case
function toKebabCase(str: string) {
  return str.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();
}

// wxs里的不能直接访问data，所以给他真实的lit 组件
export function createInstanceDescriptor(el) {

  return {

    concise: el,

    selectComponent: (selector) => {

      return getSelectorComponentsDescriptor(el, selector)
    },

    selectAllComponents: (selector) => {

      return getSelectorComponentsDescriptor(el, selector, true)
    },

    setStyle: (objOrStr) => {
      const styleTarget = el.style; // 直接操作组件的样式

      if (typeof objOrStr === 'string') {
        // 如果是字符串，直接赋值给 style 属性
        styleTarget.cssText = objOrStr;
      } else if (typeof objOrStr === 'object' && objOrStr !== null) {
        // 如果是对象，逐个属性赋值
        for (const [key, value] of Object.entries(objOrStr)) {
          styleTarget.setProperty(toKebabCase(key), value);
        }
      }
    },

    addClass: (className) => {
      el.classList.add(className); // 给组件根元素添加类
    },

    removeClass: (className) => {
      el.classList.remove(className); // 从组件根元素移除类
    },

    hasClass: (className) => {
      return el.classList.contains(className); // 检查组件根元素是否包含某个类
    },


    getDataset: () => {
      const dataset = el.dataset; // 或者 this.renderRoot.host.dataset

      return convertDataSet(dataset)

    },

    callMethod: (name, ...args) => {
      if (el.instance && el.instance[name] && typeof el.instance[name] === "function") {
        el.instance[name].call(el.instance, ...args)
      }
    },


    requestAnimationFrame: window.requestAnimationFrame.bind(window),


    triggerEvent: (...args) => {
      el.instance && el.instance.triggerEvent(...args);
    },


    setTimeout: window.setTimeout,
    clearTimeout: window.clearTimeout,


    getBoundingClientRect: () => {

      return el.getBoundingClientRect()
    }


  }


}



//@ts-ignore
export const DESIGN_BASE_WIDTH = __CONCISE_DEIGN_WIDTH__; // 设计稿宽度





const generatedCodes = new Set();

export function generateUniqueCode() {
  let code;
  do {
    code = Math.random().toString().slice(2, 6);
  } while (generatedCodes.has(code));

  generatedCodes.add(code);
  return code;
}



export function generateHash(str) {
  const hash = sha256.create();
    hash.update(str);
    const hashHex = hash.hex();
    const hashBase64 = btoa(
        String.fromCharCode(...hashHex.match(/.{2}/g).map(byte => parseInt(byte, 16)))
    ).replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');

    return hashBase64.substring(0, 6);
}



export function getElementOrShadowRoot(element: HTMLElement) {
  return element&& element.shadowRoot ? element.shadowRoot:element
}
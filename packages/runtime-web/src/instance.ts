import { SqbPageOptions, SqbComponentOptions, mapWechatProperties } from "@wosai/smart-mp-concise-runtime-base"
import { isFunction, isObject } from "@wosai/smart-mp-concise-shared";

import _ from "lodash"


export type IMeta = {
    isPage?: boolean
    isComponent?: boolean
}



export function createWebComponentInstance(options: SqbComponentOptions | SqbPageOptions, meta: IMeta = {}) {



    const config = {

        page: {
            lifetimes: {
                firstUpdated: "onReady",

                // 属性变化了
                updated: null,

                connectedCallback: "onLoad",

                disconnectedCallback: "onUnload",

                onShow: "onShow"
            }
        },
        component: {
            lifetimes: {
                firstUpdated: "ready",

                // 属性变化了
                updated: null,

                connectedCallback: "attached",

                disconnectedCallback: "detached",
                created: "created"
            }
        }
    }


    const { isComponent, isPage } = meta


    function execLiftimes(name: string, ...params: any) {

        if (isPage) {
            return execPageLiftimes.call(this, name, ...params);
        }

        const instanceName = config.component.lifetimes[name];

        //@ts-ignore
        if (options.lifetimes && options.lifetimes[instanceName]) {
            //@ts-ignore
            options.lifetimes[instanceName].call(this, ...params);
        } else if (options[instanceName]) {
            options[instanceName].call(this, ...params);
        }
    }

    function execPageLiftimes(name: string, ...params: any) {

        const instanceName = config.page.lifetimes[name];
        if (instanceName && options[instanceName]) {
            options[instanceName].call(this, ...params)
        }
    }


    let externalClassesProperties = {};

    //@ts-ignore
    (options.externalClasses || []).forEach((externalClass) => {
        externalClassesProperties[externalClass] = {
            type: String
        }
    })



    return (baseClass: any) => {

        if (!baseClass) baseClass = class { }

        return class extends baseClass {
            // 这样定义是因为lit element的属性需要静态定义
            static properties = {
                ...(super.properties || {}),
                //@ts-ignore
                ...(options.properties || {}),
                ...externalClassesProperties
            }

            constructor() {
                super();

            }



            // 不能再这里的constructor调用，函数重载的问题
            concise_init() {

                super.concise_init && super.concise_init();

                // 同步方法
                [this, this.concise_methods].forEach((source) => {
                    if (isComponent) {
                        // 组件：直接复制 methods
                        const methods = (options as SqbComponentOptions).methods || {}
                        Object.keys(methods).forEach(key => {
                            source[key] = methods[key]
                        })
                    } else if (isPage) {
                        // 页面：复制所有属性，对函数进行 this 绑定
                        Object.keys(options).forEach(key => {
                            const value = options[key]
                            if (isFunction(value)) {
                                source[key] = (...args) => value.call(this, ...args)
                            } else {
                                source[key] = value
                            }
                        })
                    }
                })

                // 同步data
                //@ts-ignore
                _.assign(this.data, options.data || {})

                if (isComponent) {
                    // 同步relations
                    //@ts-ignore
                    _.assign(this.concise_relations, options.relations);



                    //@ts-ignore
                    _.assign(this.properties, options.properties || {})

                    // 属性的初始化值赋值过来
                    //@ts-ignore
                    mapWechatProperties(options.properties || {}, {
                        nullProcess: (key, value) => {
                            this.properties[key] = this.data[key] = null
                        },
                        constructorProcess: (key, value) => {
                            this.properties[key] = this.data[key] = (new value()).valueOf()

                        },
                        objectProcess: (key, item) => {
                            this.properties[key] = this.data[key] = item.value

                        }
                    })




                    // 同步externalclasses
                    //@ts-ignore
                    _.assign(this.concise_externalClasses, options.externalClasses || [])



                    // 给behavior使用,判断relations是否包含了该concise_behavior_id
                    //@ts-ignore
                    if (this.behaviorMap && options.concise_behavior_id) {
                        //@ts-ignore
                        this.behaviorMap.set(options.concise_behavior_id, {})
                    }


                    // 属性里的observer字段
                    //@ts-ignore
                    const properties = options.properties || {};
                    Object.keys(properties).forEach((key) => {
                        const item = properties[key]
                        //@ts-ignore
                        if (isObject(item) && item.observer) {
                            //@ts-ignore
                            const observer = item.observer;

                            const callFunc = typeof observer === "string" ? this[observer] : observer
                            //@ts-ignore
                            if (!options.observers) options.observers = {}
                            //@ts-ignore
                            options.observers[key] = callFunc

                        }
                    })


                }





            }



            concise_hasBehaviorId(id: string) {
                return this.behaviorMap.has(id)
            }



            // 根据路径获取对象内部字段值 ,鸟蛋微信，搞这么复杂，麻痹的
            //'numberA, numberB'
            // 'some.subfield'
            // 'arr[12]'
            concise_getPropertyByPath(path) {
                return path.split('.').reduce((acc, part) => {
                    const match = part.match(/(\w+)\[(\d+)\]/); // 匹配数组路径 arr[12]
                    if (match) {
                        return acc[match[1]][match[2]]; // 获取数组元素
                    }
                    return acc[part];
                }, this.container);
            }
            updated(changedProperties: Map<string, any>) {

                if (!isComponent) return;

                super.updated && super.updated(changedProperties);

                const executedObservers = new Set<string>();

                changedProperties.forEach((prevValue, propName) => {
                    //@ts-ignore
                    Object.keys(options.observers || {}).forEach(observerKey => {
                        if (executedObservers.has(observerKey)) return; // 防止重复执行
                        const keys = observerKey.split(',').map(key => key.trim());

                        for (const key of keys) {
                            let shouldTrigger = false;
                            if (key === propName) {
                                shouldTrigger = true;
                            } else if (key.startsWith(propName + '.') || key.startsWith(propName + '[')) {
                                const subPath = key.slice(propName.length + 1);
                                const normalizedSubPath = subPath.startsWith('.') ? subPath.slice(1) : subPath;
                                const oldSubValue = _.get(prevValue, normalizedSubPath);
                                const newSubValue = _.get(this.container[propName], normalizedSubPath);
                                if (!_.isEqual(oldSubValue, newSubValue)) {
                                    shouldTrigger = true;
                                }
                            }
                            if (shouldTrigger) {
                                //@ts-ignore
                                options.observers[observerKey].call(this, ...keys.map(k => this.concise_getPropertyByPath(k)));
                                executedObservers.add(observerKey);
                                return; // 只要有一个 key 触发，立即执行并跳出 keys 循环
                            }
                        }
                    });
                });

            }

            concise_created() {
                super.concise_created && super.concise_created();
                isComponent && execLiftimes.call(this, "created")

            }


            connectedCallback(...params) {
                super.connectedCallback && super.connectedCallback(...params);
                execLiftimes.call(this, "connectedCallback", ...params);



            }

            disconnectedCallback(...params) {
                super.disconnectedCallback && super.disconnectedCallback(...params);
                execLiftimes.call(this, "disconnectedCallback")
            }


            firstUpdated() {
                super.firstUpdated && super.firstUpdated();

                execLiftimes.call(this, "firstUpdated");

            }


            onShow() {
                // 页面的onshow
                execLiftimes.call(this, "onShow");
            }

            onPageLifetimesShow() {
                super.onPageLifetimesShow && super.onPageLifetimesShow();
                //@ts-ignore
                if (options.pageLifetimes && options.pageLifetimes.show) {
                    //@ts-ignore
                    options.pageLifetimes.show.call(this);
                }
            }


            onPageLifetimesHide() {
                super.onPageLifetimesHide && super.onPageLifetimesHide();
                //@ts-ignore
                if (options.pageLifetimes && options.pageLifetimes.hide) {
                    //@ts-ignore
                    options.pageLifetimes.hide.call(this);
                }
            }


            getIsolateType() {
                if (options.options && options.options.styleIsolation) {
                    return options.options.styleIsolation
                }

                return super.getIsolateType && super.getIsolateType()
            }


        }


    }


}
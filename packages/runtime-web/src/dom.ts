


import {
    toDatasetJsonString
} from "./eventConvert"
import { createInstanceDescriptor , generateHash } from "./helper";
import { html } from "lit"



function toFixed(number, precision) {
    var multiplier = Math.pow(10, precision + 1),
      wholeNumber = Math.floor(number * multiplier);
    return Math.round(wholeNumber / 10) * 10 / multiplier;
  }

function getUnitRegexp(unit) {
    return new RegExp('"[^"]+"|\'[^\']+\'|url\\([^\\)]+\\)|(\\d*\\.?\\d+)' + unit, 'g');
  }


  /**
   * 
   * @param style 
   * [{prop,value}]
   * @returns 
   */

  function parseStyle(style:string) {
    // 定义一个正则表达式，用于匹配所有的 CSS 属性和值对
    const regex = /(--[^:]+|[^:]+)\s*:\s*([^;]+);?/g;
    let result = [];
    let match;
  
    // 使用正则表达式匹配所有的 CSS 属性和值
    while ((match = regex.exec(style)) !== null) {
      // match[1] 是属性名，match[2] 是属性值
      result.push({ prop: match[1].trim(), value: match[2].trim() });
    }
  
    return result;
  }



function createPxReplace(opts, viewportUnit, viewportSize) {
    return function (m, $1) {
      if (!$1) return m;
      var pixels = parseFloat($1);
      if (pixels <= opts.minPixelValue) return m;
      var parsedVal = toFixed((pixels / viewportSize * 100), opts.unitPrecision);
      return parsedVal === 0 ? '0' : parsedVal + viewportUnit;
    };
  }


  const defaultOptions = {
        unitToConvert: 'rpx',
        viewportWidth: 750,
        viewportHeight: 568, 
        unitPrecision: 5,
        viewportUnit: 'vw',
        fontViewportUnit: 'vw',  // vmin is more suitable.
        selectorBlackList: [],
        propList: ['*'],
        minPixelValue: 0,
        mediaQuery: false,
        replace: true,
        landscape: false,
        landscapeUnit: 'vw',
        landscapeWidth: 568
  }

export function normalizeStyle(str:string,options:any) {
    if (!str) return "";


    const opts = Object.assign({},defaultOptions,options)

    const rules = parseStyle(str);

    let pxRegex = getUnitRegexp(opts.unitToConvert);

    rules.forEach((rule) => {
        rule.value = rule.value.replace(pxRegex, createPxReplace(opts, opts.viewportUnit, opts.viewportWidth))
    });

    const res = rules.map(item => `${item.prop}: ${item.value}`)
    .join('; ') + ';';

    return res;
    


}



// 处理事件的名称

export function processDomEventHandler(funcOrString:Function | string) {

        return (e) => {
            if (typeof funcOrString === "function") {
                funcOrString(e,createInstanceDescriptor(this))
            }else if (typeof funcOrString === "string") {
              let func = this.instance[funcOrString];
              if (func && typeof func === "function") {
                this.instance[funcOrString](e);
              }else {
                console.error(`未找到DOM事件定义 ${funcOrString}`)
              }
            }else {
                console.warn(`未找到DOM事件 ${e.type}`)
            }

        }
}


export function processDomDatasetHandler(value:any) {
    return toDatasetJsonString(value)
}


export function processDomFor(items:any,callback:Function) {

        let lists :any

        // 检查传入的数据类型
    if (Array.isArray(items)) {
        // 如果是数组，遍历数组
            lists =  items;
      } else if (typeof items === 'string') {
        lists = items.split("");
      } else {
       
        return html``;
      }
    

      return (lists as Array<any>).map((item,index) => {
                return callback(item,index)


      } )

}


export function normalizeClasses(contents:string) {

  let constants = [];
  let dynamtics = []

  contents.split(" ").forEach((item) => {
    const externalClasses = this.getExternalClasses();

    if (externalClasses.includes(item)) {
      dynamtics.push(this[item]);
    }else {
      constants.push(item);
    }
  })

  let processedClasses:any = []
  this.getExternalClassesNames(dynamtics,(results) => {
    let dynamicClasses = []
    results.forEach((item:any) => {
      // 没有找到对应的样式
      if (typeof item !== "object") {
        constants.push(item)
      }else {
        this.insertCSSClass(item)
        dynamicClasses.push(item.name)
      }
    })

      processedClasses = processedClasses.concat(constants).concat(dynamicClasses)

  })


  processedClasses = processedClasses.join(" ")



  return processedClasses


 

}

//@ts-nocheck

import {
    DESIGN_BASE_WIDTH,
    rootComponentName
} from "./helper"


import { sqb } from "./apis/index"
import { generateId } from "@wosai/smart-mp-concise-shared";



export function concise_init() {

    concise_init_component_env()
    concise_init_page_env()

}

export function concise_init_page_env() {

    //window.wx = sqb


    window.Page = function (options) {
        window.$$concise_page_options$$ = options
    }

    window.App = function (options) {
        window.$$concise_app_options$$ = options
    }


    // window.getCurrentPages = function () {

    //     try {
    //         const children = Array.from(getApp().shadowRoot.children)
    //             .filter((child) => child._isConsiePage)
    //             .map((page) => page.instance);


    //         return children
    //     } catch (error) {
    //         return []
    //     }

    // }



    // window.getApp = function () {

    //     const appComponent = document.querySelector(rootComponentName);

    //     return appComponent
    // }






}



export function concise_init_component_env() {


    if (!window.wx) {
        window.wx = sqb;
    }
    else {
       wx.createSelectorQuery = sqb.createSelectorQuery;
       wx.createIntersectionObserver = sqb.createIntersectionObserver;
    }

    window.Component = function (options) {
        window.$$concise_component_options$$ = options;
    }

    window.Behavior = function (options) {
        if (options.concise_behavior_id) return options;

        Object.defineProperty(options, "concise_behavior_id", {
            configurable: false,
            enumerable: false,
            get() {
                return generateId()
            }
        })
        return options;
    }


    window.getRegExp = function (pattern, flags) {
        try {
            return new RegExp(pattern, flags);
        } catch (e) {
            console.error('Invalid regular expression:', e);
            return null;
        }
    }

}

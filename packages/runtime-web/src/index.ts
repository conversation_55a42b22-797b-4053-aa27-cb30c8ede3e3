

import "construct-style-sheets-polyfill"

import {
    concise_init,
    concise_init_component_env,
    concise_init_page_env
} from "./init"

import {
    defineCustomElementIfNotDefined
} from "./components/utils"


export {
    defineCustomElementIfNotDefined,
    concise_init_component_env,
    concise_init_page_env,
    concise_init
}

// import Mock from "mockjs"

concise_init();

export * from "./component"


export * from "./app"


// export {
//     Mock
// }

 export * from "./page"

export * from "./components"

export * from "./apis/index"



export* from "./dom"

export * from "./eventConvert"
import { generateId, isFunction } from "@wosai/smart-mp-concise-shared";
import { LitElement, PropertyDeclarations } from "lit";

import {
    generateUniqueCode,
    getQueryParams,
} from "./helper"

import _ from "lodash"

export class BaseElement extends LitElement {

    // createRenderRoot() {
    //     return this; // 禁用 Shadow DOM
    // }

    _isConsiePage = false

    _concise_component_type = 0

    instance: any

    concise_id = generateUniqueCode()

    static properties: PropertyDeclarations = {
        parentid: {
            type: String,
            attribute: true,
            reflect: true,
        }
    }

    getRoot() {
        return this.shadowRoot || this;
    }

    // protected createRenderRoot(): HTMLElement | DocumentFragment {
    //     return this;
    // }

    constructor() {
        super();
    }


    syncInstanceDataValue() {

        //@ts-ignore
        const compnentInstance = this.instance;
        // 数据同步过来
        const data = compnentInstance.data || {}
        //@ts-ignore
        const properties = this.constructor.properties || {};
        Object.keys(data).forEach((key) => {
            if (this[key]) {
                if (properties[key]) {
                    console.warn(`数据字段${key} 和属性的冲突了，将覆盖属性设置的值，`);
                }else {
                    console.error(`数据字段${key} 和 lit element的冲突了`);
                    return ;
                }
            }

            this[key] = _.cloneDeep(data[key])
        })

        // 同步方法过来
        const methods = compnentInstance.concise_methods || {}
        Object.keys(methods).forEach((key) => {

            if (this[key]) {
                console.warn(`方法字段${key} 和 lit element的冲突了，如果你不用于DOM点击事件，将不影响`);
                return ;
            }
            this[key] = (...args) => {
               
                if (isFunction(methods[key])) {
                    return methods[key].call(this.instance, ...args)
                } else {
                    console.error(`methods ${key} is not a function`)
                }

            }

        })

       
    }



}
import _ from "lodash"



export const ExternalClassesBehavior = (base) => {

    return class extends base {

        insertQueues = [];
        classMaps = new Map();
        isready = false;
        styleSheet: CSSStyleSheet;
        constructor() {
            super();
            this.styleSheet = new CSSStyleSheet();

        }






        getExternalClasses() {
            //@ts-ignore
            return this.instance.concise_externalClasses;
        }


        connectedCallback() {

            super.connectedCallback && super.connectedCallback();

            this.addEventListener('external-event', this.handleExternalEvent);

        }

        disconnectedCallback() {
            super.disconnectedCallback && super.disconnectedCallback();
            this.removeListener();
            this.insertQueues = [];
            this.classMaps.clear();
        }




        removeListener() {
            this.removeEventListener('external-event', this.handleExternalEvent);
            if (this.observer) {
                this.observer.disconnect();
            }

        }


        getExternalClassesNames(lists: string, callback) {
            if (!lists || !lists.length) return callback([]);
            this.dispatchEvent(
                new CustomEvent('external-event', {
                    detail: {
                        lists,
                        child: this,
                        parentId: this.parentid,
                        callback: (results) => {
                            callback(results)
                        },

                    },
                    bubbles: true,
                    composed: true,
                }))
        }



        firstUpdated() {
            this.shadowRoot.adoptedStyleSheets = [...(this.shadowRoot.adoptedStyleSheets || []), this.styleSheet];
            this.handleInsertQueues()
            this.isready = true;
        }
        handleInsertQueues() {

            while (this.insertQueues.length) {
                const item = this.insertQueues.shift();
                this._insertCSSClass(item)
            }
        }


        isRuleExists(item): boolean {
            const { name, value, type } = item;
            const trimmedValue = value.trim();
            const existingRules = Array.from(this.styleSheet.cssRules);

            return existingRules.some(rule => {
                if (type === "keyframes") {
                    return rule instanceof CSSKeyframesRule &&
                        rule.name === name &&
                        rule.cssText.trim() === trimmedValue;
                } else {
                    const valueWithSemicolon = trimmedValue.endsWith(';') ? trimmedValue : `${trimmedValue};`;

                    return rule instanceof CSSStyleRule &&
                        rule.selectorText === `.${name}` &&
                        rule.style.cssText.trim() === valueWithSemicolon;
                }
            });
        }

        _insertCSSClass(item) {


            const { name, value, type } = item;
            //@ts-ignore
            const sheet = this.styleSheet

            if (!sheet) return;
            let inserted = false;

            // 尝试插入现有样式表


            try {

                if (this.isRuleExists(item)) {
                    return; // 如果规则已存在，直接返回
                }

                if (type === "keyframes") {
                    // 对于 @keyframes 规则，直接插入完整的 cssText
                    sheet.insertRule(value, sheet.cssRules.length);
                } else {
                    // 对于普通样式规则，使用原来的方式
                    sheet.insertRule(`.${name} { ${value} }`, sheet.cssRules.length);
                }

                inserted = true;

            } catch (e) {
                console.warn("插入失败，尝试下一个样式表:", e);
            }

        }

        insertCSSClass(item: any) {

            if (!this.isready) {
                this.insertQueues.push(item)

            } else {
                this._insertCSSClass(item)
            }



            return;


        }




        handleExternalEvent = (event) => {

            const { detail: { child, parentId, lists = [], callback }, } = event

            if (child === this) return;

            if (parentId !== this.concise_id) return;

            let result = [];

            lists.forEach((item) => {
                if (!item) return;
                const classNames = item.split(" ").map((item) => item.trim()).filter((item) => !!item);

                classNames.forEach((item) => {

                    const styles = this.getStyleValues(`${item}`, child);
                    if (styles && styles.length) {
                        styles.forEach((style) => {
                            result.push({
                                ...style
                            })
                        })

                    }

                    else if (this.getExternalClasses().includes(item) && this[item]) {
                        this.dispatchEvent(new CustomEvent('external-event', {
                            detail: {
                                lists: [this[item]],
                                child: this,
                                parentId: this.parentid,
                                callback: (results) => {
                                    result.push(...results);
                                }
                            },
                            bubbles: true,
                            composed: true,
                        }));
                    }
                    else {
                        result.push(item);
                    }
                })


            });

            callback(result)


            event.stopPropagation();


        }



        getStyleValues(selector, child): Array<any> {

            if (!this.concise_component_style_sheet) {
                const styles = this.shadowRoot.querySelector('#concise-style-tag');
                if (!styles) return [];
                const contents = styles.textContent;
                this.concise_component_style_sheet = new CSSStyleSheet();
                this.concise_component_style_sheet.replaceSync(contents);
            }
            //@ts-ignore
            const sheet = this.concise_component_style_sheet;


            const reg = new RegExp(`^\\s*\\.${selector}(::?[a-zA-Z-]+)?$`)

            let res = []


            const cssRules = sheet.cssRules || sheet.rules;


            let keyframesLists = [];
            for (const rule of cssRules) {
                if (rule instanceof CSSKeyframesRule) {


                    keyframesLists.push({
                        name: rule.name,
                        value: rule.cssText,
                        type: "keyframes"
                    })
                }
            }

            for (const rule of cssRules) {
                if (rule instanceof CSSStyleRule) {


                    const { selectorText, cssText } = rule

                    const selectorGroups = selectorText.split(',').map(s => s.trim());

                    for (const selectorGroup of selectorGroups) {

                        const selectorTextLists = selectorGroup.split(/[\s>]+/);
                        let last = _.last(selectorTextLists);

                        // 2. 匹配所有类名
                        const classMatches = last.match(/\.([a-zA-Z0-9_-]+)(::?[a-zA-Z-]+)?/g);
                        if (!classMatches || classMatches.length === 0) continue;

                        last = _.last(classMatches);


                        // Check if the rule matches the selector
                        if (reg.test(last)) {

                            const parentSelector = selectorTextLists.slice(0, -1).join(' ');
                            const isParentSelectorUsed = this.isParentSelectorUsed(parentSelector, child);
                            if (!isParentSelectorUsed) continue;
                            const styleRegex = /\{([^}]+)\}/;
                            const styleMatch = cssText.match(styleRegex);
                            const styles = styleMatch ? styleMatch[1].trim() : null;
                            if (styles) {
                                // 将每个样式声明分割为数组（按分号分隔）
                                const styleArray = styles.split(';').map(style => style.trim()).filter(Boolean);

                                // 遍历样式声明，检查是否包含 `!important`
                                const updatedStyles = styleArray.map(style => {
                                    // 正则匹配样式声明中的属性和值
                                    const [property, value] = style.split(':').map(s => s.trim());


                                    const pushAnimationName = (name) => {

                                        let index = _.findIndex(keyframesLists, (item) => item.name === name);
                                        if (index !== -1) {
                                            res.push({ ...keyframesLists[index] })
                                        }


                                    }
                                    if (property === 'animation') {
                                        // animation 可能有多个动画名，取第一个为动画名
                                        const values = value.split(/\s+/);
                                        // 3. 动画名称通常是最后一个值，且不是数字、时间、关键字
                                        const name = values[values.length - 1];

                                        // 4. 验证这个值不是关键字
                                        const keywords = ['ease', 'linear', 'ease-in', 'ease-out', 'ease-in-out',
                                            'normal', 'reverse', 'alternate', 'alternate-reverse',
                                            'none', 'forwards', 'backwards', 'both',
                                            'running', 'paused'];

                                        if (!keywords.includes(name) &&
                                            !/^\d+(\.\d+)?(s|ms)$/.test(name) && // 不是时间
                                            !/^\d+$/.test(name)) { // 不是数字
                                            pushAnimationName(name)
                                        }
                                    }
                                    if (property === 'animation-name') {
                                        value.split(',').forEach(name => {
                                            const animName = name.trim();
                                            if (animName) pushAnimationName(animName);
                                        });
                                    }
                                    return style;  // 已有 `!important` 的不做修改
                                });

                                // 返回更新后的样式字符串
                                res.push({
                                    name: last.substring(1),
                                    value: ` ${updatedStyles.join('; ')} `
                                });
                            }

                        }
                    }
                }
            }








            return res;
        }

        // 检查父选择器是否在当前节点中使用
        isParentSelectorUsed(parentSelector: string, child): boolean {
            if (!parentSelector) return true; // 如果没有父选择器，直接返回 true
            var parentElements = this.shadowRoot.querySelectorAll(parentSelector);
            if (!parentElements || !parentElements.length) return false;


            // 在普通 DOM 中查找
            return Array.from(parentElements).some((parentElement) => {
                //@ts-ignore
                return parentElement.contains(child);
            })
        }



    }



}



import _ from "lodash"
import {
    property,
  } from "lit/decorators.js"
import {
    convertEvent
} from "../eventConvert"
import { COMPONENT_TYPE_USER_DEFINE, normalizeEventName } from "../helper"

//@ts-ignore
export const EmitBehavior   = (base) => {

    return class extends base {

        _concise_events = []

        //@ts-ignore
        @property({ type: Array }) concise_events = [];



        firstUpdated(changedProperties) {

            super.firstUpdated && super.firstUpdated(changedProperties);

            this.initEvent();

        }

        disconnectedCallback() {
            super.disconnectedCallback && super.disconnectedCallback();
            this.destroyEvent() ;
            this.concise_events = [];
        }


        initEvent() {

            const { concise_events = [] } = this;
            this._concise_events = concise_events.map((event) => {
                let  { name } = event
                name = this._concise_component_type & COMPONENT_TYPE_USER_DEFINE ? normalizeEventName(name) : name;
                return {
                    ...event,
                    name
                }
            })
            this._concise_events.forEach((event) => {
                this.addEventListener(event.name,this.raiseEvent)
            })
           

        }


        destroyEvent() {

            const { _concise_events = [] } = this;
            _concise_events.forEach((event) => {
                const { name } = event
                this.removeEventListener(
                 name,
                 this.raiseEvent
                  )

            })
          
        }

        raiseEvent = (e) => {

            let  { type } = e;
            const event = _.find(this._concise_events , { name : type} );
            if (!event) {
                console.warn(`why can not find event ${type} info ? ${this._concise_events}`);
                return ;
            }

            const { handler , name ,iscatch } = event;

            if (iscatch) {
                e.stopPropagation();
            }

            handler(convertEvent(e));

        }
    
    
    }
} 
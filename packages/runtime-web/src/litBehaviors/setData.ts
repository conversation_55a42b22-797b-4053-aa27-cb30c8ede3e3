
import { XData } from "@wosai/smart-mp-concise-runtime-base";

import {
    setNestedValue
} from "../helper"


// import _ from "lodash"

export const  SetData = (base) => {

  return class extends base {

    data:XData

   
    constructor() {
        super();
    }

    disconnectedCallback() {
      super.disconnectedCallback && super.disconnectedCallback();
      
    }

    // 自定义的 setData 方法，支持回调函数
  setData(newData:XData, callback:Function) {
    // const cloneData = _.cloneDeep(newData);
    Object.entries(newData).forEach(([path, value]) => {
        setNestedValue(this, path, value);
       
      });

    this.requestUpdate();

    if (callback) {
      this.updateComplete.then(() => {
        callback.call(this.instance)
      });
    }
  }


  updated(changedProperties) {

    super.updated && super.updated(changedProperties);

    this.updateInstanceProperties(Array.from(changedProperties.keys()));


   
  }


  firstUpdated(changedProperties) {
    super.firstUpdated && super.firstUpdated(changedProperties);
    this.updateInstanceProperties(Array.from(changedProperties.keys())); // 先同步数据，再执行ready生命周期
   

  }

  connectedCallback() {
     
    super.connectedCallback && super.connectedCallback();

     //  把属性值更新到instance,让小程序执行attch的时候，拿到最新的属性值
    this.updateInstanceProperties(Object.keys(this.instance.properties))

  }

  updateInstanceProperties(changedPropertyKeys) {
    const changedProppertiesMap = changedPropertyKeys.map((property) => {

        if (this.instance.properties.hasOwnProperty(property)) {
          return {
            property,
            //@ts-ignore
            value:this[property]
          }
        }
    }).filter(Boolean)


    if (changedProppertiesMap.length) {
      this.instance._concise_update_data(changedProppertiesMap)
    }

  }


  
}

}
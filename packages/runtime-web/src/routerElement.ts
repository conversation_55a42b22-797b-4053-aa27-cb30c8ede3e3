import { eventManager } from "./event";
import { css, LitElement } from 'lit';
import _ from "lodash";




import { BaseElement } from "./baseElement";
import { generateId } from "@wosai/smart-mp-concise-shared";

export type Routes = Array<{
    url: string,
    name: string
}>;


type PushStateParams = {
    url:string,
    replace?:boolean,
    id:string
}

export function pushState (params:PushStateParams) {

    let { replace,id,url } = params

    if (!url.startsWith("/")) {
        url = `/${url}`
    }
    const history = window.history
    try {
      if (replace) {

        const stateCopy = Object.assign({}, history.state)
        stateCopy.key = id;
        history.replaceState(stateCopy, '', url)
      } else {
        history.pushState({ key: id }, '', url)
      }
    } catch (e) {
      window.location[replace ? 'replace' : 'assign'](url)
    }
  }
  



  export function cleanPath (path: string): string {
    return path.replace(/\/(?:\s*\/)+/g, '/')
  }

export function getLocation(base:string = ""): string {
    let path = window.location.pathname
    const pathLowerCase = path.toLowerCase()
    const baseLowerCase = base.toLowerCase()

    if (base && ((pathLowerCase === baseLowerCase) ||
      (pathLowerCase.indexOf(cleanPath(baseLowerCase + '/')) === 0))) {
      path = path.slice(base.length)
    }
    return (path || '/') + window.location.search + window.location.hash
}


export class RouterElement extends LitElement {


    routes = [];
    static styles = css`
    :host {
      display: block;
      position: relative;
    }
    .transition-enter, .transition-leave {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      transition: opacity 0.5s ease, transform 0.5s ease;
    }
    .transition-enter {
      display:block;
      transform: translateX(100%);
    }
    .transition-enter-active {
      display:block;
      transform: translateX(0);
    }
   
    .transition-leave-active {
      display: none;
    }
  `;

   

    constructor() {
        super();

    }

    connectedCallback(): void {
        super.connectedCallback();
        this.init();
    }


   

    init() {

       

        eventManager.on("navigateTo", this.handleNavigateTo);

        eventManager.on("navigateBack", (params: any = {}) => {
            const { delta = 1 } = params;
           this.handleNavigateBack(delta);
        })


        eventManager.on("redirectTo",this.handleRedirectTo);

        this.initState();

    }


    // 维护浏览器的路径变化
    initState() {
        window.addEventListener('popstate', this.handlePopState.bind(this));
    }



    handlePopState(event) {

        console.log(`state is `,event);

        const  { state }  = event;
       
        const  url = this.getCurrentUrl();

        // 这个只能是前进了
        if (!state || !state.key) {

            this.addPage(url)

        } else{
            // const allChildren = this.getAllPages();

            // //@ts-ignore
            // const index = _.findIndex(allChildren, {concise_id : state.key})

            // // 前进的
            // if (index === -1) {

            //    throw new Error(`小程序上没有前进功能，web端禁止前进`)
            // }

            // const back_num = allChildren.length - 1 - index;

            this.handleNavigateBack(1);
        }

    }


    getAllPages(): Element[] {

        //@ts-ignore
        return Array.from(this.shadowRoot.children).filter((child) => child._isConsiePage);

    }


    showLastChild() {

        const lastChild = _.last(Array.from(this.getAllPages()))

        if (!lastChild) return;

        lastChild.classList.remove("transition-leave-active");

        //@ts-ignore
        lastChild.concise_onShow();
    }

    // 返回的路数
    handleNavigateBack = (num: number) => {


        const allChildren = this.getAllPages();
        const totalChildren = allChildren.length;

        for (let i = totalChildren - 1; i >= totalChildren - num && i >= 0; i--) {
            this.shadowRoot.removeChild(allChildren[i]);
        }

        console.log(`返回num is `,num,this.getAllPages().length)

        if (!this.getAllPages().length) {
            this.addPage();
        } else {
            this.showLastChild()
        }


    }


    handleRedirectTo = (params) => {
        let { url } = params;
        
        const allChildren = this.shadowRoot.children;

        const lastChild = _.last(allChildren)

        this.shadowRoot.removeChild(lastChild);
        requestAnimationFrame(() => {   
            this.addPage(url,true)
        })

    }

    // 处理页面跳转
    handleNavigateTo = (params) => {
        const { url } = params;

        this.addPage(url);
    }



   getCurrentUrl(path?:string) {

        // 获取当前路径
        if (!path) path = getLocation();


        if (path && path.startsWith("/")) {
            path = path.slice(1)
        }

        // 获取第一个路由
        if (!path) {
            path = this.routes[0].url;
        }


        return path;

    }

    addPage(url?:string,replace:boolean = false){

        url = this.getCurrentUrl(url);
        pushState({
            id:generateId(),
            replace,
            url
       })
        const page =  this._addPage(url);
        
    }

    // 增加页面
    _addPage(path: string) {

        const pageUrl = path.split("?")[0]
        
        const item = path ? _.find(this.routes, { url : pageUrl }) : this.routes[0];

        if (!item) {
            throw new Error(`can no find page path ${pageUrl}`);
        }

        const {name :  tag } = item;

        //@ts-ignore
        const appElement = this.shadowRoot;


        // 找到所有当前显示的页面
        let currentPages = this.getAllPages();

        currentPages = Array.from(currentPages)

        let lastChild = _.last(currentPages);


        if (lastChild) {
            lastChild.classList.add('transition-leave-active');
            //@ts-ignore
            lastChild.concise_onHide();
        }


        // 创建新页面
        const newPageTag = tag;


            const newPage = document.createElement(newPageTag);
           // newPage.classList.add('transition-enter');
            appElement.appendChild(newPage);
            // requestAnimationFrame(() => {
            //     newPage.classList.add('transition-enter-active');
            // });
            // newPage.addEventListener('transitionend', () => {
            //     newPage.classList.remove('transition-enter', 'transition-enter-active');
            // }, { once: true });

            return newPage;
        
    }


    start() {
        this.addPage();
       
    }
}

export namespace Router {
    export type options = {
        app: any // App实例
    }
}

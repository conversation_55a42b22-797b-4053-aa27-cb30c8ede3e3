

import {
    html,
    css,
    unsafeCSS,
} from "lit"
import {
    SqbComponentOptions,
    ensureComponentOptions,
    mapWechatProperties,
    ICreateComponentParams
} from "@wosai/smart-mp-concise-runtime-base";

 import boolConverter from './components/utils/bool-converter'

import {
    baseInstanceCreators,
    RelationsBehavior,
    componentInstanceCreators,

} from "./behaviors/index"

import {
    generateId, isObject, isFunction
} from "@wosai/smart-mp-concise-shared"

import {
    baseComponentCreators
} from "./litBehaviors"

import {
    COMPONENT_TYPE_LIT_ELEMENT,
    COMPONENT_TYPE_USER_DEFINE,
    composeOptions,


    reduceApplyCreator,

} from "./helper"


import _ from "lodash"
import { BaseElement } from "./baseElement";

import { BaseInstance } from "./baseInstance"
import { createWebComponentInstance } from "./instance";


function createInstanceConstructor() {

    //@ts-ignore
    let options = window.$$concise_component_options$$;

    //@ts-ignore
    delete window.$$concise_component_options$$;


    ensureComponentOptions(options)



    let result = []
    composeOptions(options, result);



    let webComponentLists: any = result.map((op) => {
        return createWebComponentInstance(op, { isComponent: true })
    })

    webComponentLists = [
        ...baseInstanceCreators,
        ...webComponentLists,
        ...componentInstanceCreators,
        RelationsBehavior
    ]

    const BaseComponent = reduceApplyCreator(webComponentLists, BaseInstance);

    return class ComponentInstance extends BaseComponent {

        route: string
        options: Record<any, any>
        __wxExparserNodeId__: string
        container: any

        constructor() {
            super()
            this.concise_init();
            this.concise_created(); //  执行小程序的created

        }

        concise_setContainer(container: any) {
            this.container = container;
        }
    }


}

export function createWebComponent(params: ICreateComponentParams) {

    let { render, styles, tag, request, componentPath } = params


    const ComponentInstanceConstructor = createInstanceConstructor();


    const webComponentLists = [
        ...baseComponentCreators,
    ]


    const _WebComponent = reduceApplyCreator(webComponentLists, BaseElement)




    let currentProperties:any = {};

    //@ts-ignore
    mapWechatProperties(ComponentInstanceConstructor.properties!, {
        nullProcess: (key, value) => {
            currentProperties[key] = {
                value: null,
                attribute:_.kebabCase(key),
                converter  :  boolConverter
            }
        },
        constructorProcess: (key, value) => {
            currentProperties[key] = {
                type: value,
                value: (new value()).valueOf(),
                attribute:_.kebabCase(key)
            }
            if (value === Boolean) {
                currentProperties[key].converter = boolConverter
            }
        },
        objectProcess: (key, item) => {
            currentProperties[key] = {
                value: _.cloneDeep(item.value),
                attribute:_.kebabCase(key)
            }

            if (item.type === Boolean) {
                currentProperties[key].converter = boolConverter
            }

            if (item.optionalTypes && item.optionalTypes.length) {
                if (item.optionalTypes.includes(Boolean)) {
                    currentProperties[key].converter = boolConverter
                }
                // 不指定类型
            }
            else if (item.type) {
                currentProperties[key].type = item.type
              
            }
        }
    })

  

    return class extends _WebComponent {

        instance: any
        suffixStyle:string
        constructor() {
            super()
            this.instance = new ComponentInstanceConstructor();
            this.instance.concise_setContainer(this);
            this.syncInstanceDataValue();
        }

        _concise_user_info = {
            request,
            componentPath,
            tag
        }

        _concise_component_type = COMPONENT_TYPE_LIT_ELEMENT | COMPONENT_TYPE_USER_DEFINE

        static properties = {
            ...currentProperties,
            ...super.properties
        }


        // static styles = css`
        //     ${unsafeCSS(styles)}
        // `;





    // 属性变化了
    updated(changedProperties) {
        super.updated && super.updated(changedProperties);
        this.instance.updated && this.instance.updated(changedProperties);
    }

    connectedCallback() {
        super.connectedCallback && super.connectedCallback()
        this.instance.connectedCallback();
    }

    disconnectedCallback() {
        this.instance.disconnectedCallback();
        super.disconnectedCallback && super.disconnectedCallback();
    }


    firstUpdated(changedProperties) {
        super.firstUpdated && super.firstUpdated(changedProperties);
        this.instance.firstUpdated(changedProperties);
    }

    

    render() {

        return html`
        <style id = "concise-style-tag"> ${styles}</style>
        ${render.call(this,this)} 

        `

    }
    };





}

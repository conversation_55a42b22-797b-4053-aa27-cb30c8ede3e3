

import {
    css,
    html,
    unsafeCSS
} from "lit"
import { ICreateComponentParams } from "@wosai/smart-mp-concise-runtime-base";



import {
    COMPONENT_TYPE_USER_DEFINE,
    composeOptions,
    getQueryParams,

    reduceApplyCreator
} from "./helper"

import {
    baseInstanceCreators,
    // pageCreators,
} from "./behaviors"

import {
    baseComponentCreators
} from "./litBehaviors"

import _ from "lodash"
import { BaseElement } from "./baseElement";


import {
    BaseInstance
} from "./baseInstance"
import { createWebComponentInstance } from "./instance";




export function createPageInstanceConstructor() {

    //@ts-ignore
    let options = window.$$concise_page_options$$;

    //@ts-ignore
    delete window.$$concise_page_options$$;


    let result = [];


    composeOptions(options, result);


    let webPageInstanceLists: any = result.map((op) => {
        return createWebComponentInstance(op , { isPage : true});
    })



    webPageInstanceLists = [
        //  ...pageCreators,
        ...webPageInstanceLists,
        ...baseInstanceCreators,

    ]



    const _PageInstance = reduceApplyCreator(webPageInstanceLists,BaseInstance);

   return  class PageInstance extends _PageInstance {

        constructor() {
            super()
            this.concise_init();
            this.initInstance();

        }

        setContainer(container: any) {
            this.container = container;
        }

        initInstance() {
            let pathname = window.location.pathname;
            if (pathname.startsWith('/')) {
                pathname = pathname.substring(1);
            }
            this.route = pathname;
            this.options = getQueryParams(window.location.search)
            
        }
    }
}

export function createPageComponent(params: ICreateComponentParams) {

    let { render, styles } = params

    const CompnentInstanceCtor = createPageInstanceConstructor();


    const webPageLists = [
        ...baseComponentCreators,
    ]


    const _WebPageComponent = reduceApplyCreator(webPageLists, BaseElement)

    return class WebPageComponent extends _WebPageComponent {

        static styles = css`
        ${unsafeCSS(styles)}
    `;


        _isConsiePage = true

        constructor() {
            super();
            this.instance = new CompnentInstanceCtor();
            this.instance.setContainer(this);
            this.syncInstanceDataValue()
        }


        connectedCallback() {
            super.connectedCallback && super.connectedCallback();
            this.instance.connectedCallback(getQueryParams(window.location.search));
        }

        disconnectedCallback() {
            super.disconnectedCallback && super.disconnectedCallback();
            this.instance.disconnectedCallback();
        }
    
    
        firstUpdated(changedProperties) {
            this.concise_onShow();
            super.firstUpdated && super.firstUpdated(changedProperties);
            this.instance.firstUpdated(changedProperties);
        }

        concise_onShow() {
           
            this.instance.onShow && this.instance.onShow();
            // 获取所有自定义的元素实例

            this.getAllCustomSelfComponents().forEach((comp) => {
                //@ts-ignore
                comp?.instance.onPageLifetimesShow && comp.instance.onPageLifetimesShow();
            })
        }

        concise_onHide() {
            this.instance.onHide && this.instance.onHide();
            this.getAllCustomSelfComponents().forEach((comp) => {
                //@ts-ignore
                comp?.instance.pageLifetimesHide && comp.instance.pageLifetimesHide();
            })
        }


        getAllCustomSelfComponents() {

            const allElements = this.getRoot().querySelectorAll('*');
            const customElements = Array.from(allElements).filter(el =>
                //@ts-ignore
                el._concise_component_type & COMPONENT_TYPE_USER_DEFINE
            );

            return customElements;
        }


        render() {
            return html`
              ${render.call(this,this)} 
            `


        }
    };





}

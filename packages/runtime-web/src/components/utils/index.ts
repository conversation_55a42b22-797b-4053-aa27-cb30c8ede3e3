export function isMobile() {
  const platform = navigator.platform;
  return /iPhone|iPad|iPod|Android/.test(platform);
}



export const addFocusToInputTypeElement = (node) => {
  const WHITE_LIST = ['input', 'textarea']
  try {
    const name = node.nodeName.toLowerCase()
    const isInputType = WHITE_LIST.some((item) => name.indexOf(item))
    // 如果不是input类型，直接返回
    if (!isInputType) return

    const findNode = (name) =>
      node && node.shadowRoot && node.shadowRoot.querySelector(name)
    const focus = (nodeArr = []) =>
      nodeArr.forEach(
        (item) => typeof item.focus === 'function' && item.focus()
      )
    const nodes = []

    WHITE_LIST.map((item) => {
      const nodeItem = findNode(item)
      nodeItem && nodes.push(nodeItem)
    })

    focus(nodes)
  } catch (e) { }
}


// 根据ua判断是否为ios系统
export const isIos = () => {
  try {
    const userAgent = navigator.userAgent
    return !!userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)
  } catch (e) {
    console.warn(`${e}`)
    return false
  }
}


const REFRESH_INTERVAL = 1000 / 60
export const requestAnimationFrame = (callback) => {
  if (typeof window.requestAnimationFrame === 'function')
    return window.requestAnimationFrame(callback)

  return setTimeout(callback, REFRESH_INTERVAL)
}


// 给定一个 propery 数组，返回 property map
export const getPropertiesByAttributes = (keys = []) => {
  const map = {}

  if (keys.length <= 0) return map
  keys.forEach((key) => (map[key] = Symbol.for(key)))

  return map
}


export function defineCustomElementIfNotDefined(name, elementClass) {
  if (!customElements.get(name)) {
    customElements.define(name, elementClass);
  }
}
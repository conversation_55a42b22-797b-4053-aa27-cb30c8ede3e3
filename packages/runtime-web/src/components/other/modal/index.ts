//@ts-nocheck

import { LitElement, html, css } from 'lit';
import { defineCustomElementIfNotDefined } from '../../utils';

class ModalElement extends LitElement {
  static styles = css`
    :host {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      justify-content: center;
      align-items: center;
      z-index: 1000;
    }

    :host(.show) {
      display: flex;
    }

    .modal {
      background-color: #fff;
      padding: 20px;
      border-radius: 5px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      text-align: center;
      max-width: 400px;
      width: 100%;
    }

    .modal-header {
      font-size: 1.2em;
      margin-bottom: 10px;
    }

    .modal-content {
      margin-bottom: 20px;
    }

    .modal-footer {
      display: flex;
      justify-content: flex-end;
    }

    .modal-footer button {
      margin-left: 10px;
    }
  `;

  static properties = {
    title: { type: String },
    content: { type: String },
    confirmText: { type: String },
    cancelText: { type: String }
  };

  constructor() {
    super();
    this.title = '';
    this.content = '';
    this.confirmText = 'Confirm';
    this.cancelText = 'Cancel';
  }

  render() {
    return html`
      <div class="modal">
        <div class="modal-header">${this.title}</div>
        <div class="modal-content">${this.content}</div>
        <div class="modal-footer">
          <button @click="${this.onCancel}">${this.cancelText}</button>
          <button @click="${this.onConfirm}">${this.confirmText}</button>
        </div>
      </div>
    `;
  }

  show({ title, content, confirmText = 'Confirm', cancelText = 'Cancel' }) {
    this.title = title;
    this.content = content;
    this.confirmText = confirmText;
    this.cancelText = cancelText;
    this.classList.add('show');
  }

  hide() {
    this.classList.remove('show');
  }

  onCancel() {
    this.hide();
    this.dispatchEvent(new CustomEvent('cancel'));
  }

  onConfirm() {
    this.hide();
    this.dispatchEvent(new CustomEvent('confirm'));
  }
}

defineCustomElementIfNotDefined('concise-modal-element', ModalElement);

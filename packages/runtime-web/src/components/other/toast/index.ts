

//@ts-nocheck
import { LitElement, html, css } from 'lit';
import { defineCustomElementIfNotDefined } from '../../utils';

export class ToastElement extends LitElement {
  static styles = css`
    :host {
      position: fixed;
      bottom: 50%;
      left: 50%;
      transform: translate(-50%,50%);

      background-color: rgba(0, 0, 0, 0.8);
      color: #fff;
      padding: 10px 20px;
      border-radius: 4px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
      z-index: 9999;
      display: none;
      align-items: center;
    }

    :host(.show) {
      display: flex;
    }

    .icon {
      margin-right: 10px;
    }

    .icon-success {
      content: url('path/to/success-icon.svg'); /* Replace with your success icon path */
    }

    .icon-error {
      content: url('path/to/error-icon.svg'); /* Replace with your error icon path */
    }

    .icon-warning {
      content: url('path/to/warning-icon.svg'); /* Replace with your warning icon path */
    }

    .icon-none {
      display: none;
    }
  `;

  static properties = {
    message: { type: String },
    icon: { type: String }
  };

  constructor() {
    super();
    this.message = '';
    this.icon = 'none';
  }

  render() {
    return html`
      <span class="icon icon-${this.icon}"></span>
      <span>${this.message}</span>
    `;
  }

  show(message, icon = 'none', duration = 3000) {
    this.message = message;
    this.icon = icon;
    this.classList.add('show');

    setTimeout(() => {
      this.hide();
    }, duration);
  }

  

  hide() {
    this.classList.remove('show');
  }
}

defineCustomElementIfNotDefined('toast-element', ToastElement);
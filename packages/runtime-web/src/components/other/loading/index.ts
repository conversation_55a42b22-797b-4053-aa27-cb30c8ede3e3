
//@ts-nocheck
import { LitElement, html, css , unsafeCSS } from 'lit';
import { defineCustomElementIfNotDefined } from '../../utils';

export class LoadingElement extends LitElement {
  static styles = css`
    :host {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      justify-content: center;
      align-items: center;
      color: white;
      font-size: 1.5em;
      z-index: 9999;
    }
    :host([active]) {
      display: flex;
    }
    .loading-message {
      margin-left: 10px;
    }
    :host([mask]) {
      background-color: rgba(0, 0, 0, 0.5);
    }
    :host(:not([mask])) {
      background-color: transparent;
    }
  `;

  static properties = {
    active: { type: Boolean, reflect: false },
    title: { type: String,value:"" },
    mask: { type: Boolean, reflect: false },
  };

  constructor() {
    super();
  }

  render() {
    return html`
      <div>
        <span class="loading-message">${this.title}</span>
      </div>
    `;
  }
}

defineCustomElementIfNotDefined('concise-loading-element', LoadingElement);

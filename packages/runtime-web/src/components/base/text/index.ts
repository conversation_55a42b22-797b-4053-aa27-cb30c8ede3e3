import { css, html,  } from 'lit'

import {
    property,
    customElement
} from "lit/decorators.js"

import { BaseElement } from '../../baseElement'
import { defineCustomElementIfNotDefined } from '../../utils'


export  class Text extends BaseElement {
  static get styles() {
    return css`
      :host {
        display: inline;

        /* autoprefixer: ignore next */
        -webkit-box-orient: vertical;
      }
    `
  }

  

  /**
   * 是否可选择
   */
  @property({ type: Boolean })
  userSelect = false

  attributeChangedCallback(name: string, oldVal: any, newVal: any) {
    super.attributeChangedCallback(name, oldVal, newVal)

    if (name === 'user-select') {
      this.selectableChanged()
    }
  }

  connectedCallback() {
    super.connectedCallback()
    this.selectableChanged()

  }



  selectableChanged() {
    if (this.userSelect) {
      this.style.setProperty('user-select', 'text')
    } else {
      this.style.setProperty('user-select', 'none')
    }
  }

  render() {
    const style = css`
      text-decoration: inherit;
    `
    return html`<span style="${style}"><slot style="${style}"></slot></span>`
  }
}

defineCustomElementIfNotDefined("concise-text",Text)
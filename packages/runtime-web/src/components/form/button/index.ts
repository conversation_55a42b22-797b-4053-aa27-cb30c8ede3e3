import { css, html, unsafeCSS } from 'lit'


import {
  property
} from "lit/decorators.js"
import { BaseElement } from '../../baseElement'
import { defineCustomElementIfNotDefined } from '../../utils'

import boolConverter from '../../utils/bool-converter'
import  { ConciseForm } from '../form'

export class ConsieButton extends BaseElement {
  protected enableHover = true

  static get styles() {
    return css`
      :host {
              -webkit-tap-highlight-color: transparent;
            background-color: #f8f8f8;
            border-radius: 5px;
            box-sizing: border-box;
            color: #000;
            cursor: pointer;
            display: block;
            font-size: 18px;
            line-height: 2.56;
            margin-left: auto;
            margin-right: auto;
            overflow: hidden;
            padding-left: 14px;
            padding-right: 14px;
            position: relative;
            text-align: center;
            text-decoration: none;
      }

      :host::after {
      
      border: 1px solid rgba(0,0,0,.2);
    border-radius: 10px;
    box-sizing: border-box;
    content: " ";
    height: 200%;
    left: 0;
    position: absolute;
    top: 0;
    -webkit-transform: scale(.5);
    transform: scale(.5);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    width: 200%;
    border-top-width: 1px;
    border-right-width: 1px;
    border-bottom-width: 1px;
    border-left-width: 1px;
    border-top-style: solid;
    border-right-style: solid;
    border-bottom-style: solid;
    border-left-style: solid;
    border-top-color: rgba(0, 0, 0, 0.2);
    border-right-color: rgba(0, 0, 0, 0.2);
    border-bottom-color: rgba(0, 0, 0, 0.2);
    border-left-color: rgba(0, 0, 0, 0.2);
    border-image-source: initial;
    border-image-slice: initial;
    border-image-width: initial;
    border-image-outset: initial;
    border-image-repeat: initial;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
    border-bottom-left-radius: 10px;
    }

      :host([disabled='true']) {
        color: rgba(0, 0, 0, 0.6);
        background-color: rgba(255, 255, 255, 0.6);
      }

      :host([isHover='true']) {
        color: rgba(0, 0, 0, 0.3);
        background-color: #ddd;
      }

      /* primary */
      :host([type='primary']) {
        color: #fff;
        background-color: #108ee9;
        border-color: #108ee9;
      }

      :host([type='primary'][disabled='true']) {
        color: rgba(255, 255, 255, 0.6);
        background-color: #9fd2f6;
        border: 0;
      }

      :host([type='primary'][isHover='true']) {
        color: rgba(255, 255, 255, 0.3);
        background-color: #0b71ba;
      }

      /* ghost */
      :host([type='ghost']) {
        color: #108ee9;
        background-color: transparent;
        border-color: #108ee9;
      }
      :host([type='ghost'][disabled='true']) {
        color: #ccc;
        background-color: #ddd;
        border: 0;
      }

      :host([type='ghost'][isHover='true']) {
        color: #fff;
        background-color: #0b71ba;
      }

      /* warn */
      :host([type='warn']) {
        color: #fff;
        background-color: #e94f4f;
        border-color: #e94f4f;
      }
      :host([type='warn'][disabled='true']) {
        color: rgba(255, 255, 255, 0.6);
        background-color: rgba(233, 79, 79, 0.4);
        border-width: 0;
      }

      :host([type='warn'][isHover='true']) {
        color: rgba(255, 255, 255, 0.3);
        background-color: #ea3c3c;
      }

      /* size 相关 */
      :host([size='mini']) {
        display: inline-block;
        font-size: 28px;
        min-width:96px;
        height: 52px;
        line-height: 48px;
        padding: 0 8px;
      }

      /* loading 相关 */
      .loading {
        display: inline-block;
        width: 0.5em;
        height: 0.5em;
        color: inherit;
        pointer-events: none;
        border: 0.1em solid currentcolor;
        border-bottom-color: transparent;
        border-radius: 50%;
        -webkit-animation: 1s loading linear infinite;
        animation: 1s loading linear infinite;
      }
      @-webkit-keyframes loading {
        0% {
          -webkit-transform: rotate(0deg);
          transform: rotate(0deg);
        }
        100% {
          -webkit-transform: rotate(360deg);
          transform: rotate(360deg);
        }
      }
      @keyframes loading {
        0% {
          -webkit-transform: rotate(0deg);
          transform: rotate(0deg);
        }
        100% {
          -webkit-transform: rotate(360deg);
          transform: rotate(360deg);
        }
      }
    `
  }

  @property({ converter: boolConverter })
  loading = false;

  @property({ type: String })
  ['form-type']

  @property({ type: String })
  formtype

  getFormType() {
    return this['form-type'] || this.formtype
  }

  @property({ type: String })
  ['open-type']

  connectedCallback() {
    super.connectedCallback()
    this.addEventListener('click', this.onClicked, false)
  }

  onClicked = () => {
    if (this.getFormType()) {
      const form = this.getForm()
      if (form) {
        switch (this.getFormType()) {
          case 'submit': {
            form.submit()
            break
          }

          case 'reset': {
            form.reset()
            break
          }
        }
      }
    }
  }

  protected getForm(): ConciseForm | null {
    const forms: any = document.querySelectorAll('concise-form')
    for (const f of forms) {
      if (f.contains(this)) {
        return f as ConciseForm
      }
    }
    return null
  }

  render() {
    return html`
      ${this.loading ? html`<div class="loading"></div>` : undefined}
      <slot></slot>
    `
  }
}


defineCustomElementIfNotDefined("concise-button",ConsieButton)
import { html } from 'lit'

import {
    state,
     property
} from "lit/decorators.js"




import { BaseElement } from '../../baseElement'
import { defineCustomElementIfNotDefined } from '../../utils'



// interface IRadioGroup {
//   selectValues: string[];
//   myArray: string[];
// }
//
// interface IRadioItem {
//   value: string;
//   checked: boolean;
// }

export  class ConciseCheckboxGroup
  extends BaseElement

{
  // static get properties() {
  //   return {
  //     selectValues: [],
  //   };
  // }

  /**
   * 组件值，选中时 change 事件会携带的 value。
   */
  @property({ type: String }) onChange = ''

  @property({ type: Array }) selectValues = []

  /**
   * 当前的value
   * 内部变量，暴露给form
   */
  @state() value = ''

  reset() {
    const namedElements = this.querySelectorAll('concise-checkbox')
    namedElements.forEach((e) => {
      ;(<any>e).checked = false
      ;(<any>e).classList.remove('a-checkbox-checked')
    })
  }

  constructor() {
    super()
    ;(<any>this).selectVal = ''
  }

  connectedCallback() {
    super.connectedCallback()
    this.addListener()
    this.initTapClick()
  }

  attributeChangedCallback(name, oldVal, newVal) {
    super.attributeChangedCallback(name, oldVal, newVal)
  }

  disconnectedCallback() {
    super.disconnectedCallback()

    this.removeEventListener(
      'call-concise-checkbox-group-event',
      this._callconciseCheckboxGroup
    )
    this.removeEventListener(
      'init-concise-checkbox-group-event',
      this._initconciseCheckboxGroup
    )
  }

  initTapClick() {
    this.addEventListener(
      'click',
      (e) => {
        const checkboxNode = this.findCheckNode(<HTMLElement>e.target)
        if (checkboxNode && !checkboxNode.disabled) {
          this.changeValueList(checkboxNode.value)
        }
      },
      true
    )
  }

  findCheckNode(node) {
    if (
      node.tagName === 'CONCISE-CHECKBOX' ||
      node.tagName === 'CONCISE-CHECKBOX-GROUP'
    ) {
      return undefined
    }
    if (node.tagName === 'CONCISE-LABEL') {
      const checkBoxNode = node.querySelector('concise-checkbox')
      if (checkBoxNode) {
        return checkBoxNode
      } else {
        return undefined
      }
    } else {
      const checkboxNodeList = node.querySelectorAll('concise-checkbox')
      if (checkboxNodeList && checkboxNodeList.length > 0) {
        return checkboxNodeList[0]
      } else if (node.parentNode) {
        const checkboxNode = node.parentNode.querySelector('concise-checkbox')
        if (checkboxNode) {
          return checkboxNode
        } else {
          return this.findCheckNode(node.parentNode)
        }
      }
    }
    return node
  }

  _callconciseCheckboxGroup = (e) => {
    const { value = '' } = (<any>e).detail || {}
    this.changeValueList(value)
  }

  _initconciseCheckboxGroup = (e) => {
    const nodeList = this.querySelectorAll('concise-checkbox')
    nodeList.forEach((item) => {
      const checkbox: any = item
      if (
        checkbox.checked &&
        !(<any>this).selectValues.some((val) => val === checkbox.value)
      ) {
        ;(<any>this).selectValues.push(checkbox.value)
      }
    })
  }

  addListener() {
    this.addEventListener(
      'call-concise-checkbox-group-event',
      this._callconciseCheckboxGroup
    )

    this.addEventListener(
      'init-concise-checkbox-group-event',
      this._initconciseCheckboxGroup
    )
  }

  changeValueList(value) {
    const valueArr = (<any>this).selectValues || []
    let newValue = []
    if (valueArr.some((item) => item === value)) {
      newValue = valueArr.filter((item) => item !== value)
    } else {
      newValue = [...valueArr, value]
    }
    ;(<any>this).selectValues = [...newValue]
    const nodeList = this.querySelectorAll('concise-checkbox')

    nodeList.forEach((item) => {
      const checkbox: any = item
      const isChecked = (<any>this).selectValues.some(
        (val) => val === checkbox.value
      )
      checkbox.checked = isChecked
      if (isChecked) {
        checkbox.classList.add('a-checkbox-checked')
      } else {
        checkbox.classList.remove('a-checkbox-checked')
      }
    })

    this.value = this.selectValues.join(',')

    this.dispatchEvent(
      new CustomEvent('change', {
        detail: {
          value: (<any>this).selectValues
        },
        bubbles: true
      })
    )
  }

  render() {
    return html` <slot></slot> `
  }
}


defineCustomElementIfNotDefined("concise-checkbox-group",ConciseCheckboxGroup)
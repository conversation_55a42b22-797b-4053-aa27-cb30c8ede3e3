import { css, html,  } from 'lit'

import {
    property
} from "lit/decorators.js"
import { BaseElement } from '../../baseElement'
import { defineCustomElementIfNotDefined } from '../../utils'

export  class ConciseLabel extends BaseElement {
  // static get styles() {
  //   return css`
  //     :host {
  //       display: flex;
  //       flex-direction: row;
  //       align-items: center;
  //     }
  //   `
  // }

  /**
   * 绑定组件的 ID。
   */
  @property({ type: String }) for = ''

  constructor() {
    super()
  }

  connectedCallback() {
    super.connectedCallback()
  }

  attributeChangedCallback(name, oldVal, newVal) {
    super.attributeChangedCallback(name, oldVal, newVal)
  }

  disconnectedCallback() {
    super.disconnectedCallback()
  }

  render() {
    return html` <slot></slot> `
  }
}


defineCustomElementIfNotDefined("concise-label",ConciseLabel)


import {
    css,
    html
} from "lit"

import {
  customElement
} from "lit/decorators.js"
import { BaseElement } from "../../baseElement";
import { defineCustomElementIfNotDefined } from "../../utils";

export  class ConciseSwiperItem extends BaseElement {
    static styles = css`
      :host {
        display: block;
        width: 100%;
        height: 100%;
      }
    `;
  
    render() {
      return html`
        <slot></slot>
      `;
    }
  }
  
  defineCustomElementIfNotDefined('concise-swiper-item', ConciseSwiperItem)
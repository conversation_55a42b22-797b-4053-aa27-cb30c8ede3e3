import { AutoplayController } from './autoplay-controller';

import { classMap } from 'lit/directives/class-map.js';
import { eventOptions, property, query, state } from 'lit/decorators.js';
import { html, LitElement } from 'lit';
import { map } from 'lit/directives/map.js';
import { prefersReducedMotion } from './animate';
import { range } from 'lit/directives/range.js';
import { watch } from './watch';
import styles from './styles';
import type { CSSResultGroup, PropertyValueMap } from 'lit';
import { defineCustomElementIfNotDefined } from '../../utils';

import { EmitBehavior } from '../../../litBehaviors';


//@ts-ignore
 const EventClass = EmitBehavior(LitElement) as new (...args: any[]) => LitElement;

// Define the carousel item type
type SlCarouselItem = HTMLElement;

/** Ensures a number stays within a minimum and maximum value */
export function clamp(value: number, min: number, max: number) {
  const noNegativeZero = (n: number) => (Object.is(n, -0) ? 0 : n);

  if (value < min) {
    return noNegativeZero(min);
  }

  if (value > max) {
    return noNegativeZero(max);
  }

  return noNegativeZero(value);
}

/** Waits for a specific event to be emitted from an element. Ignores events that bubble up from child elements. */
export function waitForEvent(el: HTMLElement, eventName: string) {
  return new Promise<void>(resolve => {
    function done(event: Event) {
      if (event.target === el) {
        el.removeEventListener(eventName, done);
        resolve();
      }
    }

    el.addEventListener(eventName, done);
  });
}

export class ConciseSwiper extends EventClass {
  static styles: CSSResultGroup = [styles];

  /** 是否采用衔接滑动（循环播放） */
  @property({ type: Boolean, reflect: true }) circular = false;

  /** 是否显示面板指示点 */
  @property({ type: Boolean, reflect: true, attribute: 'indicator-dots' }) indicatorDots = false;

  /** 指示点颜色 */
  @property({ attribute: 'indicator-color' }) indicatorColor = 'rgba(0, 0, 0, .3)';

  /** 当前选中的指示点颜色 */
  @property({ attribute: 'indicator-active-color' }) indicatorActiveColor = '#000000';

  /** 是否自动切换 */
  @property({ type: Boolean, reflect: true }) autoplay = false;

  /** 当前所在滑块的 index */
  @property({ type: Number, reflect: true }) current = 0;

  /** 自动切换时间间隔 */
  @property({ type: Number, reflect: true }) interval = 5000;

  /** 滑动动画时长 */
  @property({ type: Number, reflect: true }) duration = 500;

  /** 滑动方向是否为纵向 */
  @property({ type: Boolean, reflect: true }) vertical = false;

  /** 前边距，可用于露出前一个swiper-item */
  @property({ attribute: 'previous-margin' }) previousMargin = '0px';

  /** 后边距，可用于露出后一个swiper-item */
  @property({ attribute: 'next-margin' }) nextMargin = '0px';

  /** 同时显示的滑块数量 */
  @property({ type: Number, attribute: 'display-multiple-items' }) displayMultipleItems = 1;

  /** 是否跳过未显示的滑块布局 */
  @property({ type: Boolean, attribute: 'skip-hidden-item-layout' }) skipHiddenItemLayout = false;

  /** 指定swiper切换动画类型 */
  @property({ attribute: 'easing-function' }) easingFunction = 'default';

  /** 指定snap-to-alignment的值 */
  @property({ attribute: 'snap-to-alignment' }) snapToAlignment = 'center';

  /** 是否显示导航按钮（非微信小程序属性，保留用于Web） */
  @property({ type: Boolean, reflect: true }) navigation = false;

  /** 是否允许鼠标拖拽（非微信小程序属性，保留用于Web） */
  @property({ type: Boolean, reflect: true, attribute: 'mouse-dragging' }) mouseDragging = false;

  @query('.carousel__slides') scrollContainer: HTMLElement;
  @query('.carousel__pagination') paginationContainer: HTMLElement;

  // The index of the active slide
  @state() activeSlide = 0;

  @state() scrolling = false;

  @state() dragging = false;

  private autoplayController = new AutoplayController(this, () => this.next());
  private dragStartPosition: [number, number] = [-1, -1];
  private mutationObserver: MutationObserver;
  private pendingSlideChange = false;

  connectedCallback(): void {
    super.connectedCallback();
    this.setAttribute('role', 'region');
    this.setAttribute('aria-label', 'Carousel');
  }

  disconnectedCallback(): void {
    super.disconnectedCallback();
    this.mutationObserver?.disconnect();
  }

   firstUpdated(changedProperties): void {
    //@ts-ignore
    super.firstUpdated(changedProperties);
    setTimeout(() => {
      this.initializeSlides();
      this.mutationObserver = new MutationObserver(this.handleSlotChange);
      this.mutationObserver.observe(this, {
        childList: true,
        subtree: true
      });

      this.startAutoplay();

      this.hasUpdated = true;
    }, 30);

  }



  startAutoplay() {
    this.autoplayController.stop();
    // 在子组件加载完成后开启 autoplay
    if (this.autoplay && this.getSlides().length > 1) {
      this.autoplayController.start(this.interval);
    }
  }

  protected willUpdate(changedProperties) {
    // 同步 current 和 activeSlide
    if (changedProperties.has('current')) {
      this.activeSlide = this.current;
    }

    if (changedProperties.has('autoplay') || changedProperties.has('interval')) {
      this.startAutoplay();
    }

    // 确保 displayMultipleItems 至少为 1
    if (changedProperties.has('displayMultipleItems')) {
      this.displayMultipleItems = Math.max(1, this.displayMultipleItems);
    }
  }

  private getPageCount() {
    const slidesCount = this.getSlides().length;
    const { displayMultipleItems, circular } = this;

    const pages = circular ? slidesCount / displayMultipleItems : (slidesCount - displayMultipleItems) / displayMultipleItems + 1;

    return Math.ceil(pages);
  }

  private getCurrentPage() {
    return Math.ceil(this.activeSlide / this.displayMultipleItems);
  }

  private canScrollNext(): boolean {
    const slidesCount = this.getSlides().length;
    // 当只有一个 slide 时，禁用滚动
    if (slidesCount <= 1) {
      return false;
    }
    return this.circular || this.getCurrentPage() < this.getPageCount() - 1;
  }

  private canScrollPrev(): boolean {
    const slidesCount = this.getSlides().length;
    // 当只有一个 slide 时，禁用滚动
    if (slidesCount <= 1) {
      return false;
    }
    return this.circular || this.getCurrentPage() > 0;
  }

  /** @internal Gets all carousel items. */
  private getSlides({ excludeClones = true }: { excludeClones?: boolean } = {}) {
    return Array.from(this.children).filter(
      (el: HTMLElement) => this.isSwiperItem(el) && (!excludeClones || !el.hasAttribute('data-clone'))
    ) as SlCarouselItem[];
  }

  private handleClick(event: MouseEvent) {
    if (this.dragging && this.dragStartPosition[0] > 0 && this.dragStartPosition[1] > 0) {
      const deltaX = Math.abs(this.dragStartPosition[0] - event.clientX);
      const deltaY = Math.abs(this.dragStartPosition[1] - event.clientY);
      const delta = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

      // Prevents clicks on interactive elements while dragging if the click is within a small range. This prevents
      // accidental drags from interfering with intentional clicks.
      if (delta >= 10) {
        event.preventDefault();
      }
    }
  }

  private handleKeyDown(event: KeyboardEvent) {
    if (['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 'Home', 'End'].includes(event.key)) {
      const target = event.target as HTMLElement;
      const isFocusInPagination = target.closest('[part~="pagination-item"]') !== null;
      const isNext = event.key === 'ArrowDown' || event.key === 'ArrowRight';
      const isPrevious = event.key === 'ArrowUp' || event.key === 'ArrowLeft';

      event.preventDefault();

      if (isPrevious) {
        this.previous();
      }

      if (isNext) {
        this.next();
      }

      if (event.key === 'Home') {
        this.goToSlide(0);
      }

      if (event.key === 'End') {
        this.goToSlide(this.getSlides().length - 1);
      }

      if (isFocusInPagination) {
        this.updateComplete.then(() => {
          const activePaginationItem = this.shadowRoot?.querySelector<HTMLButtonElement>(
            '[part~="pagination-item--active"]'
          );

          if (activePaginationItem) {
            activePaginationItem.focus();
          }
        });
      }
    }
  }

  private handleMouseDragStart(event: PointerEvent) {
    const canDrag = this.mouseDragging && event.button === 0;
    if (canDrag) {
      event.preventDefault();

      document.addEventListener('pointermove', this.handleMouseDrag, { capture: true, passive: true });
      document.addEventListener('pointerup', this.handleMouseDragEnd, { capture: true, once: true });
    }
  }

  private handleMouseDrag = (event: PointerEvent) => {
    if (!this.dragging) {
      // Start dragging if it hasn't yet
      this.scrollContainer.style.setProperty('scroll-snap-type', 'none');
      this.dragging = true;
      this.dragStartPosition = [event.clientX, event.clientY];
    }

    this.scrollContainer.scrollBy({
      left: -event.movementX,
      top: -event.movementY,
      //@ts-ignore
      behavior: 'instant'
    });
  };

  private handleMouseDragEnd = () => {
    const scrollContainer = this.scrollContainer;

    document.removeEventListener('pointermove', this.handleMouseDrag, { capture: true });

    // get the current scroll position
    const startLeft = scrollContainer.scrollLeft;
    const startTop = scrollContainer.scrollTop;

    // remove the scroll-snap-type property so that the browser will snap the slide to the correct position
    scrollContainer.style.removeProperty('scroll-snap-type');

    // fix(safari): forcing a style recalculation doesn't seem to immediately update the scroll
    // position in Safari. Setting "overflow" to "hidden" should force this behavior.
    scrollContainer.style.setProperty('overflow', 'hidden');

    // get the final scroll position to the slide snapped by the browser
    const finalLeft = scrollContainer.scrollLeft;
    const finalTop = scrollContainer.scrollTop;

    // restore the scroll position to the original one, so that it can be smoothly animated if needed
    scrollContainer.style.removeProperty('overflow');
    scrollContainer.style.setProperty('scroll-snap-type', 'none');
    //@ts-ignore
    scrollContainer.scrollTo({ left: startLeft, top: startTop, behavior: 'instant' });

    requestAnimationFrame(async () => {
      if (startLeft !== finalLeft || startTop !== finalTop) {
        scrollContainer.scrollTo({
          left: finalLeft,
          top: finalTop,
          behavior: prefersReducedMotion() ? 'auto' : 'smooth'
        });
        await waitForEvent(scrollContainer, 'scrollend');
      }

      scrollContainer.style.removeProperty('scroll-snap-type');

      this.dragging = false;
      this.dragStartPosition = [-1, -1];
      this.handleScrollEnd();
    });
  };

  @eventOptions({ passive: true })
  private handleScroll() {
    this.scrolling = true;
    if (!this.pendingSlideChange) {
      this.synchronizeSlides();
    }
  }

  /** @internal Synchronizes the slides with the IntersectionObserver API. */
  private synchronizeSlides() {
    const io = new IntersectionObserver(
      entries => {
        io.disconnect();

        for (const entry of entries) {
          const slide = entry.target;
          slide.toggleAttribute('inert', !entry.isIntersecting);
          slide.classList.toggle('--in-view', entry.isIntersecting);
          slide.setAttribute('aria-hidden', entry.isIntersecting ? 'false' : 'true');
        }

        const firstIntersecting = entries.find(entry => entry.isIntersecting);
        if (!firstIntersecting) {
          return;
        }

        const slidesWithClones = this.getSlides({ excludeClones: false });
        const slidesCount = this.getSlides().length;

        // Update the current index based on the first visible slide
        const slideIndex = slidesWithClones.indexOf(firstIntersecting.target as SlCarouselItem);
        // Normalize the index to ignore clones
        const normalizedIndex = this.circular ? slideIndex - this.displayMultipleItems : slideIndex;

        // Set the index to the closest "snappable" slide
        this.activeSlide =
          (Math.ceil(normalizedIndex / this.displayMultipleItems) * this.displayMultipleItems + slidesCount) % slidesCount;

        if (!this.scrolling) {
          if (this.circular && firstIntersecting.target.hasAttribute('data-clone')) {
            const clonePosition = Number(firstIntersecting.target.getAttribute('data-clone'));

            //@ts-ignore
            this.goToSlide(clonePosition, 'instant');
          }
        }
      },
      {
        root: this.scrollContainer,
        threshold: 0.6
      }
    );

    this.getSlides({ excludeClones: false }).forEach(slide => {
      io.observe(slide);
    });
  }

  private handleScrollEnd() {
    if (!this.scrolling || this.dragging) return;
    this.scrolling = false;
    this.pendingSlideChange = false;
    this.synchronizeSlides();
  }

  private isSwiperItem(node: Node): node is SlCarouselItem {
    return node instanceof Element && node.tagName.toLowerCase() === 'concise-swiper-item';
  }

  private handleSlotChange = (mutations: MutationRecord[]) => {
    const needsInitialization = mutations.some(mutation =>
      Array.from(mutation.addedNodes).concat(Array.from(mutation.removedNodes)).some(
        (el: HTMLElement) => this.isSwiperItem(el) && !el.hasAttribute('data-clone')
      )
    );

    // Reinitialize the carousel if a carousel item has been added or removed
    if (needsInitialization) {
      this.initializeSlides();
    }

    this.startAutoplay();

    this.requestUpdate();
  };

  @watch('circular', { waitUntilFirstUpdate: true })
  @watch('displayMultipleItems', { waitUntilFirstUpdate: true })
  initializeSlides() {
    // Removes all the cloned elements from the carousel
    this.getSlides({ excludeClones: false }).forEach((slide, index) => {
      slide.classList.remove('--in-view');
      slide.classList.remove('--is-active');
      slide.setAttribute('role', 'group');
      slide.setAttribute('aria-label', `Slide ${index + 1}`);

      if (this.indicatorDots) {
        slide.setAttribute('id', `slide-${index + 1}`);
        slide.setAttribute('role', 'tabpanel');
        slide.removeAttribute('aria-label');
        slide.setAttribute('aria-labelledby', `tab-${index + 1}`);
      }

      if (slide.hasAttribute('data-clone')) {
        slide.remove();
      }
    });

    this.updateSlidesSnap();

    // 当只有一个 slide 时，禁用 circular 和克隆
    const slidesCount = this.getSlides().length;
    if (this.circular && slidesCount > 1) {
      // Creates clones to be placed before and after the original elements to simulate infinite scrolling
      this.createClones();
    }

    // Because the DOM may be changed, restore the scroll position to the active slide
    this.goToSlide(this.activeSlide, 'auto');

    this.synchronizeSlides();
  }

  private createClones() {
    const slides = this.getSlides();

    const displayMultipleItems = this.displayMultipleItems;
    const lastSlides = slides.slice(-displayMultipleItems);
    const firstSlides = slides.slice(0, displayMultipleItems);

    lastSlides.reverse().forEach((slide, i) => {
      const clone = slide.cloneNode(true) as HTMLElement;
      clone.setAttribute('data-clone', String(slides.length - i - 1));
      this.prepend(clone);
    });

    firstSlides.forEach((slide, i) => {
      const clone = slide.cloneNode(true) as HTMLElement;
      clone.setAttribute('data-clone', String(i));
      this.append(clone);
    });
  }

  @watch('activeSlide')
  handleSlideChange() {
    const slides = this.getSlides();

    // 当只有一个 slide 时，强制设置 current 为 0
    if (slides.length === 1) {
      this.current = 0;
      return;
    }

    slides.forEach((slide, i) => {
      slide.classList.toggle('--is-active', i === this.activeSlide);
    });

    // 同步 current 属性
    this.current = this.activeSlide;

    // Do not emit an event on first render
    if (this.hasUpdated) {
      // 发射微信小程序兼容的事件
      this.dispatchEvent(new CustomEvent('change', {
        detail: {
          current: this.activeSlide,
          source: 'autoplay' // 或者 'autoplay', 'touch', 'nav'
        },
        bubbles: false,
        composed: false
      }));
    }
  }

  @watch('displayMultipleItems')
  updateSlidesSnap() {
    const slides = this.getSlides();

    const displayMultipleItems = this.displayMultipleItems;
    slides.forEach((slide, i) => {
      const shouldSnap = (i + displayMultipleItems) % displayMultipleItems === 0;
      if (shouldSnap) {
        slide.style.removeProperty('scroll-snap-align');
      } else {
        slide.style.setProperty('scroll-snap-align', 'none');
      }
    });
  }

  /**
   * Move the carousel backward by `displayMultipleItems` slides.
   *
   * @param behavior - The behavior used for scrolling.
   */
  previous(behavior: ScrollBehavior = 'smooth') {
    this.goToSlide(this.activeSlide - this.displayMultipleItems, behavior);
  }

  /**
   * Move the carousel forward by `displayMultipleItems` slides.
   *
   * @param behavior - The behavior used for scrolling.
   */
  next(behavior: ScrollBehavior = 'smooth') {
    this.goToSlide(this.activeSlide + this.displayMultipleItems, behavior);
  }

  /**
   * Scrolls the carousel to the slide specified by `index`.
   *
   * @param index - The slide index.
   * @param behavior - The behavior used for scrolling.
   */
  goToSlide(index: number, behavior: ScrollBehavior = 'smooth') {
    const { displayMultipleItems, circular } = this;

    const slides = this.getSlides();
    const slidesWithClones = this.getSlides({ excludeClones: false });

    // No need to do anything in case there are no items in the carousel
    if (!slides.length) {
      return;
    }

    // 当只有一个 slide 时，不需要滚动
    if (slides.length === 1) {
      this.activeSlide = 0;
      return;
    }

    // Sets the next index without taking into account clones, if any.
    const newActiveSlide = circular
      ? (index + slides.length) % slides.length
      : clamp(index, 0, slides.length - displayMultipleItems);
    this.activeSlide = newActiveSlide;

    // Get the index of the next slide. For looping carousel it adds `displayMultipleItems`
    // to normalize the starting index in order to ignore the first nth clones.
    const nextSlideIndex = clamp(
      index + (circular ? displayMultipleItems : 0),
      0,
      slidesWithClones.length - 1
    );

    const nextSlide = slidesWithClones[nextSlideIndex];

    this.scrollToSlide(nextSlide, prefersReducedMotion() ? 'auto' : behavior);
  }

  private scrollToSlide(slide: HTMLElement, behavior: ScrollBehavior = 'smooth') {
    // Since the geometry doesn't happen until rAF, we don't know if we'll be scrolling or not...
    // It's best to assume that we will and cleanup in the else case below if we didn't need to
    this.pendingSlideChange = true;
    window.requestAnimationFrame(() => {
      // This can happen if goToSlide is called before the scroll container is rendered
      // We will have correctly set the activeSlide in goToSlide which will get picked up when initializeSlides is called.
      if (!this.scrollContainer) {
        return;
      }

      const scrollContainer = this.scrollContainer;
      const scrollContainerRect = scrollContainer.getBoundingClientRect();
      const nextSlideRect = slide.getBoundingClientRect();

      const nextLeft = nextSlideRect.left - scrollContainerRect.left;
      const nextTop = nextSlideRect.top - scrollContainerRect.top;

      if (nextLeft || nextTop) {
        // This is here just in case someone set it back to false
        // between rAF being requested and the callback actually running
        this.pendingSlideChange = true;
        scrollContainer.scrollTo({
          left: nextLeft + scrollContainer.scrollLeft,
          top: nextTop + scrollContainer.scrollTop,
          behavior
        });
      } else {
        this.pendingSlideChange = false;
      }
    });
  }

  render() {
    const { displayMultipleItems, scrolling } = this;
    const slidesCount = this.getSlides().length;
    const pagesCount = this.getPageCount();
    const currentPage = this.getCurrentPage();
    const prevEnabled = this.canScrollPrev();
    const nextEnabled = this.canScrollNext();

    return html`
      <div part="base" class="carousel">
        <div
          id="scroll-container"
          part="scroll-container"
          class="${classMap({
      carousel__slides: true,
      'carousel__slides--horizontal': !this.vertical,
      'carousel__slides--vertical': this.vertical,
      'carousel__slides--dragging': this.dragging
    })}"
          style="--slides-per-page: ${this.displayMultipleItems}; --previous-margin: ${this.previousMargin}; --next-margin: ${this.nextMargin};"
          aria-busy="${scrolling ? 'true' : 'false'}"
          aria-atomic="true"
          tabindex="0"
          @keydown=${this.handleKeyDown}
          @mousedown="${this.handleMouseDragStart}"
          @scroll="${this.handleScroll}"
          @scrollend=${this.handleScrollEnd}
          @click=${this.handleClick}
        >
          <slot></slot>
        </div>

        ${this.navigation && slidesCount > 1
        ? html`
              <div part="navigation" class="carousel__navigation">
                <button
                  part="navigation-button navigation-button--previous"
                  class="${classMap({
          'carousel__navigation-button': true,
          'carousel__navigation-button--previous': true,
          'carousel__navigation-button--disabled': !prevEnabled
        })}"
                  aria-label="Previous slide"
                  aria-controls="scroll-container"
                  aria-disabled="${prevEnabled ? 'false' : 'true'}"
                  @click=${prevEnabled ? () => this.previous() : null}
                >
                  <slot name="previous-icon">
                    <sl-icon library="system" name="chevron-left"></sl-icon>
                  </slot>
                </button>

                <button
                  part="navigation-button navigation-button--next"
                  class=${classMap({
          'carousel__navigation-button': true,
          'carousel__navigation-button--next': true,
          'carousel__navigation-button--disabled': !nextEnabled
        })}
                  aria-label="Next slide"
                  aria-controls="scroll-container"
                  aria-disabled="${nextEnabled ? 'false' : 'true'}"
                  @click=${nextEnabled ? () => this.next() : null}
                >
                  <slot name="next-icon">
                    <sl-icon library="system" name="chevron-right"></sl-icon>
                  </slot>
                </button>
              </div>
            `
        : ''}
        ${this.indicatorDots && slidesCount > 1
        ? html`
              <div part="pagination" role="tablist" class="carousel__pagination">
                ${map(range(pagesCount), index => {
          const isActive = index === currentPage;
          return html`
                    <button
                      part="pagination-item ${isActive ? 'pagination-item--active' : ''}"
                      class="${classMap({
            'carousel__pagination-item': true,
            'carousel__pagination-item--active': isActive
          })}"
                      role="tab"
                      id="tab-${index + 1}"
                      aria-controls="slide-${index + 1}"
                      aria-selected="${isActive ? 'true' : 'false'}"
                      aria-label="${isActive
              ? `Slide ${index + 1}`
              : `Go to slide ${index + 1} of ${pagesCount}`}"
                      tabindex=${isActive ? '0' : '-1'}
                      @click=${() => this.goToSlide(index * displayMultipleItems)}
                      @keydown=${this.handleKeyDown}
                      style="background-color: ${isActive ? this.indicatorActiveColor : this.indicatorColor};"
                    ></button>
                  `;
        })}
              </div>
            `
        : ''}
      </div>
    `;
  }
}


defineCustomElementIfNotDefined('concise-swiper', ConciseSwiper);
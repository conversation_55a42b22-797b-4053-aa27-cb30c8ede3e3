export interface ConciseEventInit<T = any> extends EventInit {
  detail?: T
  other?: object
}

export class ConciseEvent<T> extends Event {
  detail: T
  other: object
  constructor(typeArg: string, eventInitDict?: ConciseEventInit<T>) {
    super(typeArg, eventInitDict)
    this.detail = eventInitDict.detail
    this.other = eventInitDict.other
    if (this.other) {
      Object.assign(this, this.other)
    }
  }
}






  

  

  export function toDatasetJsonString(value: any) {

      switch (typeof value) {
        case 'string': {
          return value
        }
        case 'object': {
          if (value == null) {
            return '$.nul()'
          }
          return `$.value(${JSON.stringify(value)})`
        }
        case 'function': {
          return ''
          // throw new Error('dataset 数据不允许传 function');
        }
        case 'undefined': {
          return '$.undefined()'
        }
        case 'bigint':
        case 'number':
        case 'boolean': {
          return `$.value(${value})`
        }
        default: {
          return `$.value(${value})`
        }
      }
    }
  
  
  const DataSetParser = {
    nul() {
      return null
    },
    undefined() {
      return undefined
    },
    value(v) {
      return v
    }
  }
  
  const DATA_SET_PARSER_REGEXP = /^\$\.(value|nul|undefined)\(/
  


  /**
   * 解析dataset value
   * @param {*} str
   */
  export function parseDatasetValue(str: any) {

    /*#__NOINLINE__*/
    const $ = DataSetParser

    if (!$)
      console.log($)
    try {
      // 限定 eval 的使用范围
      // 仅用于 DataSetParser 中包含的方法
      // 其他情况直接返回字符串
      if (typeof str === 'string' && DATA_SET_PARSER_REGEXP.test(str)) {
        return eval(str)
      } else {
        return str
      }
    } catch (e) {
      return str
    }
  }
  

const NO_DETAIL_EVENT = ['regionchange']

export  function convertEvent (e) {
  const target = e.target && convertTarget(e.target)
  try {
    // 在支付宝小程序，e.target.dataset 取的是 e.currentTarget.dataset
    const currentTargetDataset =
      (e.currentTarget && convertTarget(e.currentTarget).dataset) || {}
    target.dataset = { ...target.dataset, ...currentTargetDataset }
  } catch (e) {}

  // 回传参数不用detail包装直接返回
  if (NO_DETAIL_EVENT.includes(e.type)) {
    return {
      stopPropagation:e.stopPropagation.bind(e),
      ...e.detail,
      nativeEvent: e,
      currentTarget:
        e.currentTarget &&
        (e.target === e.currentTarget
          ? target
          : convertTarget(e.currentTarget)),
      target,
      timeStamp: new Date().valueOf()
    }
  }

  const info = {
    stopPropagation:e.stopPropagation.bind(e),
    nativeEvent: e,
    type: e.type,
    detail: e.detail,
    target: target,
    currentTarget:
      e.currentTarget &&
      (e.target === e.currentTarget ? target : convertTarget(e.currentTarget)),
    timeStamp: new Date().valueOf(),
    ...e.other
  }

  switch (e.type) {
    case 'scroll': {
      info.detail = scrollDetail(e)
      break
    }
  }
  return info
}

function convertTarget(el) {
  return {
   // id: el.id,
    dataset: convertDataSet(el.dataset),
   // tagName: el.tagName // TODO: tagName转换成标准的小程序标签名称
  }
}
export function convertDataSet(dataset) {
  const ds = {}
  if (dataset) {
    //  这里的转换，主要是为了 dataset 支持对象或者数组
    Object.keys(dataset).forEach((key) => {
      const value = dataset[key]
      ds[key] = parseDatasetValue(value)
    })
  }
  return ds
}

/**
 * 滚动事件的detail
 * @param {*} e
 */
function scrollDetail(e) {
  const currentTarget = e.currentTarget
  return {
    scrollTop: currentTarget.scrollTop,
    scrollLeft: currentTarget.scrollLeft,
    scrollHeight: currentTarget.scrollHeight,
    scrollWidth: currentTarget.scrollWidth
  }
}




export const getDataSet = (el: HTMLElement) => {
  const result = { dataset: {} }
  try {
    const { dataset } = el
    if (!dataset) return result

    result.dataset = Object.keys(dataset).reduce((pre, key) => {
      pre[key] = parseDatasetValue(dataset[key])
      return pre
    }, {})

    return result
  } catch (e) {
    return result
  }
}
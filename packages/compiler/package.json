{"version": "2.0.3", "name": "@wosai/smart-mp-concise-compiler", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "maintainers": ["<PERSON><PERSON><PERSON><PERSON>"], "scripts": {"build": "tsc", "dev": "tsc -w"}, "selfBuild": true, "dependencies": {"@babel/core": "^7.24.7", "@babel/generator": "^7.24.7", "@babel/parser": "^7.24.7", "@babel/plugin-proposal-decorators": "^7.24.7", "@babel/plugin-transform-runtime": "^7.24.7", "@babel/preset-env": "^7.25.3", "@babel/preset-typescript": "^7.24.7", "@babel/template": "^7.25.0", "@babel/traverse": "^7.25.3", "@babel/types": "^7.25.2", "@prettier/sync": "^0.5.2", "@types/webpack": "^5.28.5", "babel-loader": "^9.1.3", "babel-plugin-transform-commonjs": "^1.1.6", "copy-webpack-plugin": "^12.0.2", "css-loader": "^7.1.2", "enhanced-resolve": "^5.17.0", "ensure-posix-path": "^1.1.1", "file-loader": "^6.2.0", "fs-extra": "^11.2.0", "glob": "^10.4.5", "html-webpack-plugin": "^5.6.0", "htmlparser2": "^9.1.0", "is-core-module": "^2.5.0", "js-beautify": "^1.15.1", "json5-loader": "^4.0.1", "less": "^4.2.0", "less-loader": "^12.2.0", "loader-utils": "^3.3.1", "lodash": "^4.17.21", "postcss": "^8.4.39", "postcss-less": "^4.0.1", "postcss-loader": "^8.1.1", "postcss-px-to-viewport": "^1.1.1", "postcss-pxtorem": "^6.1.0", "posthtml": "^0.16.6", "prettier": "^2.8.8", "qrcode-terminal": "^0.12.0", "relative": "^3.0.2", "required-path": "^1.0.1", "reserved-words": "^0.1.2", "style-loader": "^4.0.0", "typescript": "^5.5.3", "webpack": "^5.92.1", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.2", "@wosai/smart-mp-concise-shared": "workspace:*"}, "engines": {"node": ">=18.20.0"}}
import { ResolveOptions } from "webpack"





export type XData = Record<any,any>

// 支持内联样式
export type IStyleRule = {

    include:Array<RegExp | string > ,// 包括的文件
    exclude:Array<RegExp | string>,// 剔除的文件
    viewportWidth : number , // 写代码时候按的设计稿的宽度 
    unitToConvert:string,
    viewportUnit:string,
    minPixelValue:number
}

export type ICompilerOptions = {
    customEntries ? :XData,
    inputs?: string[] | XData | string,
    outputPath ? :string,
    production?:boolean,
    resolve?: ResolveOptions,
    alias:XData,
    target : "wechat" | "alipay" | "web",
    context:string // 项目根目录
    
    compileType  : 'miniprogram' | "component",
    srcPath:string,// 源码目录
    plugins?:Array<any>
    watch:boolean,
    debug?:boolean,
    externals ? :Array<any>

    processNodeModules?:boolean | { include ? :Array<RegExp>, exclude ? :Array<RegExp> }

    define?:XData,

    styleRule:Array<IStyleRule> // style 规则

    componentConfigJson?:XData // 内部使用,

    apiMock?:string, // 端接口文件
    requestMock?:string , // request请求mock
    copy:Array<any>
    entryOptions?:{
        name:string,
        page:string,
        params?:string
    }
    html ? :{
        link?:Array<string>
        style?:Array<string>
    },
    optimization?:any
}


export type BaseDep = {
    aliasPath:string,
    path:string,
}




declare module 'webpack' {
    interface Compiler {
      __concise__?: any;
    }

    interface NormalModuleCreateData {
        concise_module_info ? :any
    }
}




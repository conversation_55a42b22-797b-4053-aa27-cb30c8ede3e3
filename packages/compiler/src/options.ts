import { ICompilerOptions, XData } from "./types";

import _ from "lodash"
import { logger } from "./logger";
import webpack from "webpack"
import { glob } from "glob"
import { fileIsExist, getContext, getMiniFileName, isAlipay, isWeb, getComponentJsonFile,readFileSync } from "./util";
import { createResolver } from "./resolver";
const CopyWebpackPlugin = require('copy-webpack-plugin');
import { getAppJsonValidPages,  getComponentJsonValidDependencies, parseComponentJsonDependencies } from "./loaders/helper";
import path from "path";
import JSON5 from "json5";


const defaultOptions: Partial<ICompilerOptions> = {
    target: "wechat",
    compileType: "miniprogram",
    watch: false,

    resolve: {
        extensions: ['.ts', '.js', '.wxml', '.less',".wxss", ".wxs",'.json5'],  // 自动解析这两种文件类型的扩展名
        mainFields: ['module', 'main'],
        modules: ["node_modules"],
        symlinks: false,
        plugins: []
    },
    srcPath: "src",
    inputs: {},
    define: {
        __CONCISE_DEIGN_WIDTH__: 750, // 设计稿宽度，默认750
    }, // 变量替换

    plugins: [],

    externals: [],

    html:{
        style:[],
        link:[]
    }

}


function setOuputPath(options: Partial<ICompilerOptions>) {

    const { outputPath, target, compileType } = options;
    const configContext = process.cwd();
    if (outputPath) {
        options.outputPath = `${configContext}/${outputPath}`
    } else {

        options.outputPath = `${configContext}/dist/${target}${compileType === "component" ? "-components" : ""}`
    }


}


function setProjectContext(options: Partial<ICompilerOptions>) {
    const { srcPath } = options;
    const context = path.join(process.cwd(), srcPath)
    options.context = context;
}



function setDefineConfig(options: Partial<ICompilerOptions>) {

    let { define } = options;
    Object.keys(define).forEach((key) => {
        define[key] = JSON.stringify(define[key])
    })
}


function getOptionsComponentConfig(options :  Partial<ICompilerOptions>) {

        const { customEntries,context } = options;
        const pwd = process.cwd();
        if (customEntries && customEntries["component.json"]) {
           

            return path.join(pwd,customEntries["component.json"])

        }

        return path.join(context,"./component.json")
}

function setWebProgramEntry(options: Partial<ICompilerOptions>) {
    const { context } = options
    options.inputs = { "app.js": glob.sync(`${context}/app.{t,j}s`)[0] }
}



function setWebComponentsEntry(options: Partial<ICompilerOptions>) {
    const { context } = options
    const componentConfigJson = JSON5.parse(readFileSync(getOptionsComponentConfig(options)));
    options.componentConfigJson = componentConfigJson;
    const entryFile = `${context}/${componentConfigJson.main}`;
    const outName = getMiniFileName(entryFile, context);
    options.inputs[`${outName}`] = entryFile

}


let componentResolver: any
function parseJson(src: string, options: Partial<ICompilerOptions>) {

    let dependencies = new Set();
    const map = getComponentJsonValidDependencies({
        request: src,
        externals: options.externals
    })

    const importComponents:any = Object.values(map);

    for (let aliasPath of importComponents) {

        const tra_value = parseComponentJsonDependencies({ resolver: componentResolver, src, dep: aliasPath })
        if (tra_value) {
            dependencies.add(tra_value)
        }

    }

    return Array.from(dependencies)

}


function setEntries(entries: Array<string>, options: Partial<ICompilerOptions>) {

    if (!componentResolver) {
        componentResolver = createResolver({
            ...options.resolve,
            extensions: [".ts", ".js"],
        })
    }

    entries.forEach((file) => {
        if (!Object.values(options.inputs).includes(file)) {
            options.inputs[getMiniFileName(file, options.context)] = file;
        }
        const jsonFile = getComponentJsonFile(file);
        if (!fileIsExist(jsonFile)) {
            console.error(`component json file is not exsit`,file)
            return ;
        }
        const dependencies = parseJson(jsonFile, options);
        setEntries(dependencies as string[], options)

    })

}

function setMiniProgramEntry(options: Partial<ICompilerOptions>) {

    const { context } = options;
    const appFile = glob.sync(`${context}/app.{t,j}s`)[0];
    options.inputs = { "app.js": appFile };
    const appJson = getComponentJsonFile(appFile)
    let pages = getAppJsonValidPages({request:appJson })

    pages = pages.map((page) => {
        return glob.sync(`${path.join(context,page)}.{t,j}s`)[0];
        
    })
    setEntries(pages, options)

}

function setMiniProgramComponentsEntry(options: Partial<ICompilerOptions>) {




    const { context } = options
    const componentConfigJson = JSON5.parse(readFileSync(getOptionsComponentConfig(options)));
    options.componentConfigJson = componentConfigJson;

    let publicComponents = componentConfigJson.publicComponents || [];


    publicComponents = Array.isArray(publicComponents) ? publicComponents : Object.values(publicComponents);

    let files = []
    publicComponents.forEach((comp) => {
        let entryFile = `${context}/${comp}`;
        entryFile = glob.sync(`${entryFile}.{t,j}s`)[0];
        if (!entryFile) return;
        files.push(entryFile)
    })

    setEntries(files, options);

}

function setEntryConfig(options: Partial<ICompilerOptions>) {


    const { compileType, target } = options;


    switch (target) {

        case "web":
            if (compileType === "miniprogram") {
                setWebProgramEntry(options)
            } else {
                setWebComponentsEntry(options);
            }
            break;

        case "wechat":
        case "alipay":
            if (compileType === "miniprogram") {
                setMiniProgramEntry(options)
            } else {
                setMiniProgramComponentsEntry(options)
            }


    }

}

export function setPlugins(options: Partial<ICompilerOptions>) {

        const { copy ,define } = options

        options.plugins.push(
          new webpack.DefinePlugin(define)
        )
        if (copy) {

            const processed = copy.map((item) => {
                const context = process.cwd();
                return {
                    from :  path.join(context,item.from),
                    to :  item.to
                }

            })

          options.plugins.push(
            new CopyWebpackPlugin({
              patterns:processed,
            }),
          )
        }
}


export function setResolve(options: Partial<ICompilerOptions>) {
    const { target } = options

    options.resolve.alias['@wosai/smart-mp-concise-runtime'] = target !== "web" ? '@wosai/smart-mp-concise-runtime-mini' : '@wosai/smart-mp-concise-runtime-web';
}

export function ensureOptions(options: Partial<ICompilerOptions>): ICompilerOptions {

    if (!options.resolve) options.resolve = {}
    options.resolve.alias = options.alias || {};

   
    let mergedOptions  = _.mergeWith({},defaultOptions,options, (objValue, srcValue) => {
        if (Array.isArray(objValue)) {
          return objValue.concat(srcValue);  // 数组拼接
        }
      });

      setResolve(mergedOptions);
    setDefineConfig(mergedOptions)
    setProjectContext(mergedOptions);
    setOuputPath(mergedOptions);
    setEntryConfig(mergedOptions)
    setPlugins(mergedOptions)

    return mergedOptions as ICompilerOptions

}




export function getWebpackOuputConfig(options: Partial<ICompilerOptions>) {

    const { outputPath, compileType, target } = options

    const globalObject = isWeb() ? "window" : isAlipay() ? "my" : "wx";

    const pwd = process.cwd();
    const packageJson = JSON5.parse(readFileSync(`${pwd}/package.json`));
    const name = packageJson.name;
    const version = packageJson.version;

    const chunkLoadingGlobal = `concise-${compileType}-${name}-${version}`
    let output: any = {
        path: outputPath,
        clean: false,
        filename: '[name]',
        globalObject,
        chunkLoadingGlobal,
        publicPath: '/'
    }


    if (compileType === "component" && target === "web") {
        output.library = "concise"
        output.libraryTarget = "umd"

    }

    return output

}
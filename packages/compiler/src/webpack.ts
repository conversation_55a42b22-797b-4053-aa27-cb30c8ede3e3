



import webpack from "webpack"

import fse from "fs-extra"
import WebpackDevServer  from 'webpack-dev-server';

import {
  ConsiceMiniPlugin,

  WebpackConciseWebPlugin,
} from "./webpack-plugins"

import { ICompilerOptions } from "./types";
import { isWeb,getLocalIpAddress } from "./util";


import _ from "lodash"

import { setUserOptions } from "./user-options";

import {
  ensureOptions, getWebpackOuputConfig
} from "./options"

// 引入 qr-terminal 和 os 模块
var qrcode = require('qrcode-terminal');




export function pack(options: ICompilerOptions) {

  return new Promise((resolve, reject) => {
    options = ensureOptions(options);
    setUserOptions(options)
    process.env.CONCISE_CONTEXT = options.context;
    process.env.CONCISE_PLATFORM = options.target
    process.env.CONCISE_DEBUG = options.debug ? "true" : null

    process.env.CONCISE_COMPILE_TYPE = options.compileType
    let plugins = options.plugins

    if (isWeb()) {
      plugins.push(new WebpackConciseWebPlugin())

    } else {

      plugins.push(new ConsiceMiniPlugin())


    }

    fse.removeSync(options.outputPath)

    //@ts-ignore
    let config: webpack.Configuration = {
      context: options.context,
      watch:false,
      devtool: false,
      mode: options.production ? "production" : "development",
      entry: options.inputs,
      output: getWebpackOuputConfig(options),
      plugins,
      resolve: options.resolve,
      externals:options.externals,
      
    };



    const compiler = webpack(config);


    if (!options.watch) {

      return compiler.run((err, stats: any) => {
        if (err || stats.hasErrors()) {
          console.error('Webpack compilation errors:', err || stats.toJson().errors);
        }

        resolve(null)
      })
    }

    //@ts-ignore
    if (isWeb() && options.compileType === "miniprogram") {


      // 配置 Webpack Dev Server
      const devServerOptions = {
        client: {
          overlay: false, // 禁用 overlay
        },
        static: {
          directory: options.outputPath, // 替代 contentBase
        },
        devMiddleware: {
          writeToDisk: true,  // 允许写入磁盘
        },
        compress: true,
        // open: true, // 自动打开浏览器
        hot: true,  // 启用热模块替换
        historyApiFallback: true,
        onAfterSetupMiddleware: function (server) {


          const localIpAddress = getLocalIpAddress();
          if (localIpAddress) {
            // 获取局域网 IP 地址并构建 URL
            let localUrl = `http://${localIpAddress}:${server.options.port}`;
            if (options.entryOptions) {
              const { page , params } = options.entryOptions;
              localUrl  = `${localUrl}/${page}?${params}`
            }
            console.log(`Server started: ${localUrl}`);
            // 打印局域网二维码
            qrcode.generate(localUrl, { small: true });
          } else {
            console.log('无法获取局域网 IP 地址');
          }
        },
      };

      // 创建 Webpack Dev Server 实例
      const server = new WebpackDevServer(devServerOptions, compiler);
      compiler.hooks.failed.tap('ErrorLoggingPlugin', (error) => {
        console.error('编译过程中的错误:', error);
      });

      //启动开发服务器
      server.startCallback((options) => {
       
       // console.log(`Webpack Dev Server is running on http://localhost:${devServerOptions.port}`);

      });


    } else {
      compiler.watch({}, (err, stats: any) => {
        if (err || stats.hasErrors()) {
          console.error('Webpack compilation errors:', err || stats.toJson().errors);
        }
      })
    }


  })


}
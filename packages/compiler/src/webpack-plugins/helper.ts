

import { isFunction } from "lodash"
import { getUserOptions } from "../user-options"
import {
    isScriptFile,
    isWxmlFile,
    isWxsFile,
    isJsonFile,
    isStyleFile,
    isNpmModule,
    normalizePath,
    getFileExtName,
    isJson5File
} from "../util"

import {
    WebReplaceViewPlugin,
    FixCssImportPlugin
} from "./post-css-plugins"

// 解析的内容,只是组件的文件类型
export type IParsedResult = {
    type: "wxs" | "wxml" | "style" | "script" | "json"

} | undefined


import _ from "lodash"


export function parseReuqest(request: string) {

    //@ts-ignore
    let res: IParsedResult = {}

    if (isScriptFile(request)) {
        res!.type = "script"
    } else if (isJsonFile(request)) {
        res!.type = "json"
    } else if (isWxmlFile(request)) {

        res!.type = "wxml"
    } else if (isWxsFile(request)) {
        res!.type = "wxs"
    } else if (isStyleFile(request)) {
        res!.type = "style"
    } else {
        return undefined
    }

    return res;

}


export function getBabelLoaderConfig() {

    let config: any = {
        loader: 'babel-loader',
        options: {
            configFile: false, // 忽略 babel.config.js
            babelrc: false,    // 同时忽略 .babelrc
            presets: [
                '@babel/preset-env',
                '@babel/preset-typescript',

            ],
            plugins: [
                ['@babel/plugin-proposal-decorators', { legacy: true }],
                //["transform-commonjs"]
                // 可以在这里添加你需要的 Babel 插件
            ],
        }
    }



    return config;

}


export function createCondition(regexpOrFunction: RegExp | Function) {


    return function (file: string) {
        //@ts-ignore
        const isConditionFile = isFunction(regexpOrFunction) ? regexpOrFunction(file) : regexpOrFunction.test(file)

        return isConditionFile;
        // if (isConditionFile) {
        //     if (isNpmModule(file) && !canHandleNodeModulesFiles(file)) return false;
        //     return true;

        // }

        // return false;

    }
}




export function createWebRuleUse() {

    const babelLoderConfig = getBabelLoaderConfig();
    const webProgramLoaderPath = normalizePath("loaders/webpack-web-loader");
    const webInsertmockLoaderPath = normalizePath("loaders/web/insert-mock");
    const typeLoaderProcessInfo: any = {
        style: [
            {
                loader: 'css-loader',
                ident: 'css-loader',
                options: {
                    modules: false,
                }
            },
            {
                loader: 'postcss-loader',
                ident: 'postcss-loader',
                options: {
                    postcssOptions: {
                        plugins: [
                  
                            WebReplaceViewPlugin,
                        ],
                    },
                },
            },

            {
                loader: "less-loader",
                ident: 'less-loader',
                options: {
                    lessOptions: {
                        plugins: [
                            new FixCssImportPlugin()
                        ]
                    }
                }

            },
            {
                loader:webProgramLoaderPath,
                ident: 'concise-web-less-loader',
            }
        ],
        wxml: [{ loader: webProgramLoaderPath }],
        json: [{ loader: webProgramLoaderPath }],
        wxs: [{ ...babelLoderConfig }, { loader: webProgramLoaderPath, options: {} }],
        script: [
            { ...babelLoderConfig }, 
            { loader: webProgramLoaderPath, options: {} },
            { loader: webInsertmockLoaderPath }
        ]
    }


    return function (info) {
        const { resource } = info;

        const parsed = parseReuqest(resource)

        if (!parsed) return;

        const { type } = parsed;


        const processLoaders = typeLoaderProcessInfo[type];

        if (!processLoaders) return;

        let current_processLoaders = _.cloneDeep(processLoaders);

        if (resource === `${getUserOptions().context}/app.less`) {

            //@ts-ignore
            current_processLoaders.unshift({
                loader: "style-loader",

            })
        }


        if (type === "style" && getFileExtName(resource) !== ".less") {
            _.remove(current_processLoaders, { loader :"less-loader"})
        }
   

        if (isJson5File(resource)) {
            current_processLoaders.unshift({loader:'json5-loader' ,  type: 'javascript/auto' })
        }


        return current_processLoaders

    }
}



export function createMiniProgramRules() {


    const babelLoderConfig = getBabelLoaderConfig();
    const miniProgramLoaderPath = normalizePath("loaders/webpack-miniproram-loader");
    const typeLoaderProcessInfo = {
        style: [



            {
                loader: miniProgramLoaderPath
            },

            {
                loader: 'postcss-loader',
                ident: 'postcss-loader',
                options: {
                    postcssOptions:{
                        plugins:[]
                    }
                }
            },

            {
                loader: "less-loader",

            }
        ],
        wxml: [{ loader: miniProgramLoaderPath }],
        json: [{ loader: miniProgramLoaderPath }],
        wxs: [{ loader: miniProgramLoaderPath }],
        script: [{ ...babelLoderConfig }, { loader: miniProgramLoaderPath }]
    }


    return function (info) {
        const { resource  } = info;


        

        const parsed = parseReuqest(resource)

        if (!parsed) return;

        const { type } = parsed;


        const processLoaders = typeLoaderProcessInfo[type];

        if (!processLoaders) return;

        let current_processLoaders = _.cloneDeep(processLoaders);

        if (type === "style" && getFileExtName(resource) !== ".less") {
            _.remove(current_processLoaders, { loader :"less-loader"})
        }

        if (type === "script" && isNpmModule(resource)) {

            _.remove(current_processLoaders, { loader :"babel-loader"})
    }


        return current_processLoaders

    }

}




// 所有组件的文件作为入口文件，给webpack处理
// json/wxml/wxs/style 放到一个chunkName去，经过对应的loader和file-loader处理后,直接输出，在beforechunk的时候删除掉


import webpack, {
    Compilation,
    Compiler,
    NormalModule,

} from "webpack"
const { RawSource } = require('webpack-sources');

//@ts-ignore
import _, { chunk } from "lodash"

import fse from "fs-extra"

import {
    IParsedResult,
    parseReuqest,
    getBabelLoaderConfig,
    createCondition,
    createMiniProgramRules
} from "./helper"
import { emitFile, getContext, getMiniFileName, isAppFile, isComponentFile, isNpmModule, isScriptFile, normalizePath, readFileSync } from "../util"


const { ConcatSource } = require('webpack-sources')
const ensurePosix = require('ensure-posix-path')
const requiredPath = require('required-path')



import path from "path";

import {
    BasePlugin
} from "./base-plugin"






export class ConsiceMiniPlugin extends BasePlugin {

    isMini = true





    setRules() {

        const { compiler } = this


        compiler.options.module.rules = [

            {
                test: createCondition(/\.(js|ts|wxs)$/),

                use: createMiniProgramRules()
            },

            {
                test: createCondition(/\.(wxss|less)$/),
                use: createMiniProgramRules()
            },

            {
                test: createCondition(/\.(json|json5)$/),
                use: createMiniProgramRules()
            },
            {
                test: createCondition(/\.(wxml)$/),
                use: createMiniProgramRules()
            }
        ]
    }


    setOptimization() {


        // 动态设置 optimization 选项
        this.compiler.options.optimization = {
            ...this.getBaseOptimization(),
            splitChunks: {
                chunks: "all",
                cacheGroups: {
                    vendor: {
                        test: /.+/,
                        minChunks: 1,
                        name: function (context:any) {
                            const { request } = context

                            const src = request.split('!').pop();

                            // 所有的非组件的文件，打包到一个文件里去
                            if (!isComponentFile(src)
                                && !isAppFile(src)) {
                                return "concise.c.js"
                            }

                            return getMiniFileName(src)
                        },
                        chunks: 'all',
                        enforce: true,
                    }
                },
            },
        }

    }


    apply(compiler: Compiler) {

        super.apply(compiler);
        //@ts-ignore
        this.setOptimization();
        this.setRules();



        compiler.hooks.thisCompilation.tap('ConciseWebpackPlugin', (compilation, { normalModuleFactory }) => {

            compilation.hooks.beforeChunkAssets.tap("ConciseWebpackPlugin", () => {

                webpack.javascript.JavascriptModulesPlugin.getCompilationHooks(
                    compilation
                ).render.tap("ConciseWebpackPlugin", (source, context) => {

                    // 非入口模块 或 包含runtime
                    if (
                        context.chunkGraph.getNumberOfEntryModules(context.chunk) === 0 ||
                        context.chunk.hasRuntime()
                    ) {
                        return source
                    }

                    // 收集动态入口所有的依赖
                    const dependences = new Set()
                    context.chunk.groupsIterable.forEach((group) => {
                        group.chunks.forEach((chunk) => {
                            const filename = ensurePosix(
                                //@ts-ignore
                                path.relative(path.dirname(context.chunk.name), chunk.name)
                            )
                            if (chunk === context.chunk) return
                            dependences.add(filename)
                        })

                    })

                    // 没有依赖
                    if (dependences.size == 0) return
                    // 源文件拼接依赖
                    let concatStr = ';'
                    dependences.forEach((file) => {
                        concatStr += `require('${requiredPath(file)}');\n`
                    })
                    return new ConcatSource(concatStr, source)
                })
            })

        })





    }



}
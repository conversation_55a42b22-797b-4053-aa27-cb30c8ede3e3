

import {
  Compiler,
} from "webpack"
import { BasePlugin } from "./base-plugin"

const HtmlWebpackPlugin = require('html-webpack-plugin');


import {

  getWebFileName, isComponentFile, isScriptFile,

} from "../util"

import {
  createCondition,
  createWebRuleUse,

} from "./helper"

//@ts-ignore
import _ from "lodash"
import { getUserOptions } from "../user-options"


function getHtmlInjectStyles() {
  let userConfig = getUserOptions();
  let { html: { style = [] } } = userConfig

  return style.join("\n")

}

export class WebpackConciseWebPlugin extends BasePlugin {




  setRules() {

    const { compiler } = this


    compiler.options.module.rules = [

      {
        test: createCondition(/\.(js|ts|wxs)$/),
        use: createWebRuleUse()
      },

      {
        test: createCondition(/\.(wxss|less)$/),
        use: createWebRuleUse()
      },

      {
        test: createCondition(/\.(json|json5)$/),
        use: createWebRuleUse()
      },
      {
        test: createCondition(/\.(wxml)$/),
        use: createWebRuleUse()
      }
    ]
  }




  setOptimization() {
    const userConfig = getUserOptions();
    const { compileType } = userConfig

    if (compileType === "miniprogram") {
      this.setProgramOptimization();
    } else {
      this.setComponentLibOptimization();
    }
  }

  setComponentLibOptimization() {

    // 动态设置 optimization 选项
    //@ts-ignore

    // const userConfig = getUserOptions();
    // if (userConfig.production) {
    //   this.compiler.options.optimization = {
    //     usedExports: false,  // 禁用 tree shaking
    //     sideEffects: true,  // 禁用副作用检查
    //     minimize: true,      // 启用压缩
    //     concatenateModules: false  // 禁用作用域提升


    //   }
    // }
  }

  setProgramOptimization() {
    const userConfig = getUserOptions();

    // 动态设置 optimization 选项
    this.compiler.options.optimization = {
      ...this.getBaseOptimization(),
      ...userConfig.optimization,
      splitChunks: {
        chunks: "all",
        cacheGroups: {
          vendor: {
            test: /node_modules/,
            minChunks: 1,
            name: function (context: any) {
              const { request } = context
              const src = request.split('!').pop();
              return getWebFileName(src)
            },
            chunks: 'all',
            enforce: true
          },
          commons: {
            test: /(?!node_modules).+\.(js|ts|wxs|wxml)$/, // 匹配  目录下的文件，但排除 node_modules
            minChunks: 2,
            name: 'commons.js',
            chunks: 'all',
            enforce: true,
            priority: -10, // 确保优先级高于默认分割
          },
        },
      },
    }

  }


  setPlugins() {
    // const userConfig = getUserOptions();
    // const { compileType, context } = userConfig
    // const { plugins } = this.compiler.options;

    // if (compileType === "miniprogram") {
    //   plugins.push(
    //     new HtmlWebpackPlugin({
    //       inject: true, // 禁用自动注入的JS和CSS
    //       templateContent: ({ htmlWebpackPlugin }) => `
    //         <!DOCTYPE html>
    //         <html lang="en">
    //           <head>
    //             <meta charset="UTF-8" />
    //             <meta name='viewport' content='width=device-width,initial-scale=1.0,minimum-scale=1,maximum-scale=1,user-scalable=no' />
    //             <style>
    //               body {
    //                 margin: 0px;
    //               }
    //             </style>
    //             ${getHtmlInjectStyles()}
    //             <title>扫码点单H5</title>
    //           </head>
    //           <body>
    //             <concise-app></concise-app>
    //           </body>
    //         </html>
    //       `,
    //       title: 'My Concise App', // 动态设置标题
    //     })
    //   )
    // }


  }

  apply(compiler: Compiler) {

    super.apply(compiler);
    this.setOptimization();
    this.setRules();
    this.setPlugins();
  }

}
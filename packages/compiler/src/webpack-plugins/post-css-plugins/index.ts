
import { mapTags } from '../../util';




// 内部标签替换
let WebReplaceViewPlugin = (opts = {}) => {
    return {
        postcssPlugin: 'postcss-replace-view',
        Once(root, { result }) {
            root.walkRules((rule) => {

                const before = rule.selector;
                for (let tag of mapTags) {
                    const reg = new RegExp(`(?<![a-zA-Z0-9_\.\#-])${tag}(?![a-zA-Z0-9_-])`, 'g');
                    rule.selector = rule.selector.replace(reg, `concise-${tag}`)
                }
            });
        }
    };
};

//@ts-ignore
WebReplaceViewPlugin.postcss = true;





class FixCssImportPlugin {
  constructor() {
    //@ts-ignore
    this.minVersion = [3, 0, 0];
  }

  install(less, pluginManager) {
    pluginManager.addPreProcessor({
      process: (src) => src.replace(/@import\s+\(css\)/g, '@import (less)')
    });
  }
}



export {
  FixCssImportPlugin,

    WebReplaceViewPlugin
}
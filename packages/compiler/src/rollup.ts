

// // build.js
// import { rollup } from 'rollup';
// import resolve from '@rollup/plugin-node-resolve';
// import commonjs from '@rollup/plugin-commonjs';
// import alias from '@rollup/plugin-alias';




// import json from '@rollup/plugin-json';


// import { 
//   stylePlugin,
//     wxmlPlugin,
//     wxsPlugin,
//     customOutputPlugin,
//     componentJsonPlugin,
//     startPlugin
//  } from "./plugins/index"
// import { XData , ICompilerOptions } from './types';
// import {
//   getFileRelativePath
// } from "./util"


// function transformAliasToRollupFormat(alias:XData) {


//   let res = [];
//   for (let key in alias) {

//     res.push({
//       find:key,
//       replacement:alias[key]
//     })
//   }

//   return res;

// }


// export async function build(options:ICompilerOptions) {

//   const aliasEntries = transformAliasToRollupFormat(options.alias)
//   const bundle = await rollup({
//     input: options.inputs,
//     plugins: [

      
//       alias({
//         entries: transformAliasToRollupFormat(options.alias)
//       }),
//       resolve({
//         extensions:['.ts','.js','.wxs','.wxml','.less']
//       }),
//       commonjs(),
//       json(),
//      // lessPlugin(),
//       wxmlPlugin(),
//       wxsPlugin(),
//       customOutputPlugin(),
//       startPlugin(options),
//       componentJsonPlugin(),
//       stylePlugin()
//     ],
//     external:["node_modules"],
   

//   });


//   await bundle.write({
//     dir: options.output.dist,
//     format: 'esm',
//     manualChunks(id) {
//       if (id.includes('node_modules')) {
//          return `npm/`+ id.replace(/.*node_modules\/(.*)/, function($0, $1) {
//           return $1;
//         }).replace(/\.js/,"")
//       }
//     }
//   });
// }

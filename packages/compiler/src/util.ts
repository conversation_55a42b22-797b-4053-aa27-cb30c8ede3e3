


import fse from "fs-extra"
import path, { basename } from "path"
import fs from "fs"
import { ICompilerOptions } from "./types";
import { glob } from "glob";
const beautify = require('js-beautify').html;
const relative = require("relative");
const generate = require('@babel/generator').default;


const { parse: babelParser } = require("@babel/parser");


import { getUserOptions } from "./user-options";

import os from 'os';
// 获取局域网 IP 地址的函数
export function getLocalIpAddress() {
  const networkInterfaces = os.networkInterfaces();
  for (let interfaceName in networkInterfaces) {
    const networkInterface = networkInterfaces[interfaceName];
    for (let i = 0; i < networkInterface.length; i++) {
      const iface = networkInterface[i];
      // 判断是否为局域网 IP 地址（IPv4 且非 127.0.0.1）
      if (iface.family === 'IPv4' && iface.address !== '127.0.0.1') {
        return iface.address;
      }
    }
  }
  return null; // 如果没有找到局域网 IP 地址
}



const generatedNumbers = new Set();
// 生成6为随机数字
export function generateId() {
  let uniqueNumber;
  do {
    const now = Date.now();
    uniqueNumber = (now % 1000000).toString().padStart(6, '0');
  } while (generatedNumbers.has(uniqueNumber));

  generatedNumbers.add(uniqueNumber);
  return `${uniqueNumber}`;
}


export function getFileExtName(src: string) {
  return path.extname(src);
}


export function getFileRelativePath(src: string, target: string) {
  let rel = relative(src, target);
  if (!/^\./.test(rel)) rel = `./${rel}`;
  return rel;
}



export function isNpmModule(path: string) {
  return /node_modules/.test(path);
}


export function isNil(value: any) {
  return value === undefined || value === null
}


export const delimiters = ['{{', '}}']


export const NO = () => false

export const NOOP = () => { }

export function readFileSync(file: string): string {
  return fse.readFileSync(file, 'utf-8')
}


// // 查找同级其他后缀的文件
// export function GetSameDirectoryFile(path: string, replace: string) {
//   return path.replace(/(.*)\..+$/, function ($0, $1) {
//     return `${$1}${replace}`;
//   });
// }


export function getContext(path: string) {
  if (!path) return "";
  return path.replace(/(.*)\/(.+)/, function ($0, $1) {
    return $1;
  });
}


export function getComponentStyleFile(src: string) {

  const context = getContext(src);
  const { name } = path.parse(src);
  const styleFile = glob.sync(`${context}/${name}.{less,wxss}`)[0];

  return styleFile

}


export function getComponentJsonFile(src: string) {

  const context = getContext(src);
  const { name } = path.parse(src);
  const jsonFile = glob.sync(`${context}/${name}.{json,json5}`)[0];

  return jsonFile

}


export function getComponentTemplateFile(src: string) {

  const context = getContext(src);
  const { name } = path.parse(src);
  const templateFile = glob.sync(`${context}/${name}.wxml`)[0];

  return templateFile

}


export function getComponentScriptFile(src: string) {

  const context = getContext(src);
  const { name } = path.parse(src);
  const scriptFile = glob.sync(`${context}/${name}.{t,j}s`)[0];

  return scriptFile

}



export function isComponentFile(path: string) {
  let scriptFile = getComponentScriptFile(path);
  let templateFile = getComponentTemplateFile(path);
  let jsonFile = getComponentJsonFile(path);
  return (
    templateFile&&
    scriptFile &&
    jsonFile
  );
}

export function isComponentJsonFile(src: string) {
  return  isJsonFile(src) && isComponentFile(src)
}

export function isWxmlFile(src: string) {
  return getFileExtName(src) === ".wxml"
}

export function isWxsFile(src: string) {
  return getFileExtName(src) === ".wxs"
}


export function isStyleFile(src: string) {
  return getFileExtName(src) === ".less" || getFileExtName(src) === ".wxss"
}


export function isScriptFile(src: string) {
  const ext = getFileExtName(src);
  return ext === ".js" || ext === ".ts"
}

export function isJsonFile(src: string) {
  const ext = getFileExtName(src);
  return ext === ".json" || ext === ".json5"
}

export function isJson5File(src:string) {
    const ext = getFileExtName(src);
  return ext === ".json5"
}

export function isComponentScriptFile(src: string) {
  return isScriptFile(src) && isComponentFile(src)
}

export function fileIsExist(path: string) {
  return fse.existsSync(path);
}


const absolutePath = /^(?:\/|(?:[A-Za-z]:)?[\\|/])/;

function isAbsolute(path: string) {
  return absolutePath.test(path);
}



export function relativeId(id: string,base = process.cwd()) {
  if (typeof process === 'undefined' || !isAbsolute(id)) return id;
  return path.relative(base, id);
}


export function ensureDirectoryExistence(filePath: string) {
  const dirname = path.dirname(filePath);
  if (fs.existsSync(dirname)) {
    return true;
  }
  ensureDirectoryExistence(dirname);
  fs.mkdirSync(dirname);
}




export function emitFile(path: string, code: string) {
  fse.removeSync(path);
  fse.outputFileSync(path, code);
}


let toString = Object.prototype.toString

export const isObject = (value: any) => {
  if (value === null || toString.call(value) !== '[object Object]') return false
  return true
}



export function beautifyHtml(code: string) {

  const options = {
    indent_size: 2,
    space_in_empty_paren: true
  };

  return beautify(code, options);
}



export function getRelativePath(src: string, dist: string) {
  let rel = relative(src, dist);
  if (!/^\./.test(rel)) rel = `./${rel}`;
  return rel;
}


export function getComponentName(src: string) {

  return src.replace(/[\/\.]/g, "");
}




function deleteNodeModulePath(request) {
  return request.replace(/.*node_modules\/(.*)/, function ($0, $1) {
    return `${$1}`;
  });
}




/**
 * 
 * @param src 文件源地址
 * @param context  工程目录
 * @returns 
 */
export function getMiniFileName(src: string,context?:string) {
   context = context || process.env.CONCISE_CONTEXT;
  if (isNpmModule(src)) {
    return `npm/${deleteNodeModulePath(src)}`
  }
  let result = src.replace(`${context}/`, "")
  .replace(/\.ts$/, ".js")
  .replace(/\.json5$/, ".json")
  .replace(/\.less$/, `${isWechat()?".wxss":".acss"}`)
  .replace(/\.wxml$/, `${isWechat()?".wxml":".axml"}`)
  .replace(/\.wxs$/, `${isWechat()?".wxs":".sjs"}`)
  return result
}


export function getWebFileName(src: string,context?:string) {
  context = context || process.env.CONCISE_CONTEXT;
 if (isNpmModule(src)) {
   return `npm/${deleteNodeModulePath(src).replace(/\.(t|j)s$/, "")}.js`
 }
 let result = `${src.replace(`${context}/`, "").replace(/\.(t|j)s$/, "")}.js`
 return result
}




const tagMap = new Map();

export function getTagName(src: string) {


  const userConfig = getUserOptions();
  const { componentConfigJson } = userConfig

  // 用户自定义的组件名称
  if (componentConfigJson && componentConfigJson.publicComponents) {
   const  publicComponents = componentConfigJson.publicComponents
      if (!Array.isArray(publicComponents)) {
          const projectContext = userConfig.context;
          for (const key in publicComponents) {
            const value = publicComponents[key];
            if (`${projectContext}/${value}` === src.replace(/\.[^.]+$/, '')) return key;
          }
      }

  }
  const { dir,name } = path.parse(src)
  // 获取文件夹名称
  const folderName = path.basename(path.dirname(src));
  // 获取文件名称

  const mapKey = `${dir}/${name}`;


  if (tagMap.has(mapKey)) return tagMap.get(mapKey)

  const result = `concise-${folderName}-${name}-${generateId()}`.toLowerCase();
  tagMap.set(mapKey,result);


  return result
}


export function normalizePath(tp: string) {
  return path.resolve(__dirname, `./${tp}`)
}


export function isWeb() {
  return process.env.CONCISE_PLATFORM === "web"
}


export function isWechat() {
  return process.env.CONCISE_PLATFORM === "wechat"
}


export function isAlipay() {
  return process.env.CONCISE_PLATFORM === "alipay"
}

export function isAppFile(src: string) {
  const context = process.env.CONCISE_CONTEXT;
  const baseName = src.replace(`${context}/`,"");

  const matches = ["app.ts","app.js","app.less","app.wxss","app.json" , "app.json5"]

  return matches.includes(baseName)
}



export function generateCodeByAst(ast) {
  return generate(ast).code;
}



export function readAst(code: string,options = {}) {

  return babelParser(code, {
    // parse in strict mode and allow module declarations
    sourceType: "module",

    plugins: [
      "typescript",
    ],

    ...options
  });
}



// 去掉头尾双括号
export function deleteBrackets(value: any) {
  if (isNil(value)) return null;
  //@ts-ignore
  return value.replace(/^\s*{{\s*(((?!}})[\s\S])+)\s*}}\s*$/, ($0, $1) => $1); //去除开头结尾首位的空格
}




export function getTemplateFunctionName(name:string) {

  return `template_${name.replace(/-/g, "_")}`;
}


export const isString = (val: unknown): val is string => typeof val === 'string'



// 内部组件的信息
export const mapTags = [
  "view",
  "text",
  "icon",
  "progress",
  "scroll-view",
  "switch",
  "form",
  "input",
  "radio",
  "radio-group",
  "checkbox",
  "checkbox-group",
  "rich-text",
  "button",
  "label",
  "slider",
  "textarea",
  "image",
  "swiper",
  "swiper-item"
]


const generatedCodes = new Set();

export function generateUniqueCode() {
  let code;
  do {
    code = Math.random().toString().slice(2, 6);
  } while (generatedCodes.has(code));

  generatedCodes.add(code);
  return code;
}
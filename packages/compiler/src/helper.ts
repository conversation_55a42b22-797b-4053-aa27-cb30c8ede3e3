




//@ts-ignore
import _, { isObject } from "lodash"


import {
    getFileRelativePath,
    isWeb,
    isAlipay,
    isComponentFile,
    getMiniFileName,
    getRelativePath,
    isAppFile
} from "./util"

import path from "path"

import fs from "fs"



const traverse = require('@babel/traverse').default;

const generate = require('@babel/generator').default;

const t = require('@babel/types');
import template from "@babel/template";
import { ICompilerOptions } from "./types";
import { getUserOptions } from "./user-options";




export function getTargetRelativePathBySource(src, target) {

    const srcDist = getMiniFileName(src);
    const targetDist = getMiniFileName(target);

    return getRelativePath(srcDist, targetDist);
}


export const NEED_DELETE_PREFIX = "_NEEDDELETE"




// 替换运行时相关的api
// -> Component -> SqbComponent
// -> wx.xxx->sqb.xxxx

export function replaceRuntime() {


}



export const COMPONENT_CALL = 1;
export const PAGE_CALL = 1 << 1;
export const APP_CALL = 1 << 2
export const NATIVE_API_CALL = 1 << 3
export const BEHAVIOR_CALL = 1 << 4



const COMPONENT_RUNTIME_NAME = "ConsiceComponent";
const PAGE_RUNTIME_NAME = "ConcisePage";
const APP_RUNTIME_NAME = "ConciseApp";
const BEHAVIOR_RUNTIME_NAME = "ConciseBehavior";
const API_RUNTIME_NAME = "ConciseWx";


export function ensureMiniProgramRuntime(ast, request: string) {
    const isComponent = isComponentFile(request);
    let hasUseFlag = 0;
    traverse(ast, {

        Identifier(path) {
            let { parentPath, node } = path;
            let parentNode = parentPath.node;

            if (node.name !== "wx") return;

            if (
                parentPath &&
                parentNode.object &&
                !parentNode.computed &&
                parentNode.property === node

            ) return; // 对象的子属性

            if (
                parentPath.isObjectProperty() &&
                parentNode.key === node
            ) return; // 对象的key

            hasUseFlag |= NATIVE_API_CALL
            node.name = API_RUNTIME_NAME;
        },

        CallExpression(path) {
            const callee = path.node.callee;
            const { type, name } = callee
            if (type !== "Identifier") return;
            if (name === 'Component') {

                hasUseFlag |= COMPONENT_CALL;
                callee.name = COMPONENT_RUNTIME_NAME;

                // 你可以在这里做更多处理或打印一些信息
            }

            if (name === 'Page') {

                callee.name = PAGE_RUNTIME_NAME;
                hasUseFlag |= PAGE_CALL;

                // 你可以在这里做更多处理或打印一些信息
            }

            if (name === 'App') {

                callee.name = APP_RUNTIME_NAME;
                hasUseFlag |= APP_CALL;

                // 你可以在这里做更多处理或打印一些信息
            }


            if (name === 'Behavior') {
                hasUseFlag |= BEHAVIOR_CALL;
                callee.name = BEHAVIOR_RUNTIME_NAME;
                // 你可以在这里做更多处理或打印一些信息
            }
        },

    });

    if (hasUseFlag) {

        let s = `
        import { 
            ${hasUseFlag & COMPONENT_CALL ? `${COMPONENT_RUNTIME_NAME},` : ""}  
            ${hasUseFlag & PAGE_CALL ? `${PAGE_RUNTIME_NAME},` : ""}  
            ${hasUseFlag & APP_CALL ? `${APP_RUNTIME_NAME},` : ""}
             ${hasUseFlag & BEHAVIOR_CALL ? `${BEHAVIOR_RUNTIME_NAME},` : ""}
               ${hasUseFlag & NATIVE_API_CALL ? `${API_RUNTIME_NAME}` : ""}
        } from "@wosai/smart-mp-concise-runtime";
        
    `
        const templateCreate = template.ast`${s}`
        ast.program.body.unshift(templateCreate);
    }

}


function preprocessCodeWithRegex(code: string, platform: string, file: string) {
    let ifdefRegex, endifRegex, ifndefRegex;

    const { ext } = path.parse(file)
    switch (ext) {
        case '.js':
        case '.ts':
        case '.less':
        case '.wxs':
        case ".json5":
            // 支持单行注释 // #ifdef, // #ifndef 和 // #endif
            ifdefRegex = /\/\/\s*#ifdef\s+(\w+)/g;
            endifRegex = /\/\/\s*#endif/g;
            ifndefRegex = /\/\/\s*#ifndef\s+(\w+)/g;
            break;
        case '.wxml':
            // 支持 HTML/WXML 注释 <!-- #ifdef, #ifndef 和 #endif -->
            ifdefRegex = /<!--\s*#ifdef\s+(\w+)\s*-->/g;
            endifRegex = /<!--\s*#endif\s*-->/g;
            ifndefRegex = /<!--\s*#ifndef\s+(\w+)\s*-->/g;
            break;
        default:
            return code;
    }

    const lines = code.split('\n');
    let result = [];
    let isInsideIfdefBlock = false;
    let shouldKeepBlock = false;

    for (let i = 0; i < lines.length; i++) {
        let line = lines[i];

        // 匹配 #ifdef 指令
        let ifdefMatch = ifdefRegex.exec(line);
        if (ifdefMatch) {
            const condition = ifdefMatch[1];
            isInsideIfdefBlock = true;
            shouldKeepBlock = condition === platform;
            continue; // 跳过 #ifdef 行本身
        }

        // 匹配 #ifndef 指令
        let ifndefMatch = ifndefRegex.exec(line);
        if (ifndefMatch) {
            const condition = ifndefMatch[1];
            isInsideIfdefBlock = true;
            shouldKeepBlock = condition !== platform;
            continue; // 跳过 #ifndef 行本身
        }

        // 匹配 #endif 指令
        if (endifRegex.test(line)) {
            isInsideIfdefBlock = false;
            shouldKeepBlock = false;
            continue; // 跳过 #endif 行本身
        }

        // 保留代码，如果不在 #ifdef 或 #ifndef 块中，或者我们应该保留这个块
        if (!isInsideIfdefBlock || shouldKeepBlock) {
            result.push(line);
        }
    }

    return result.join('\n');
}

// Example usage: 读取文件并预处理它
export function processIfDefPlatformFile(source: string,filePath:string) {
    const platform = process.env.CONCISE_PLATFORM;
    const code = source;
    const processedCode = preprocessCodeWithRegex(code, platform, filePath);

    return processedCode;
}




export function canHandleNodeModulesFiles(file: string) {

    const { processNodeModules } = getUserOptions()

    if (processNodeModules === true) return true;

    if (isObject(processNodeModules)) {

        const { include = [], exclude = [] } = processNodeModules

        return isConditionTrue({ include, exclude, file })


    }

}


export function isConditionTrue({
    include = [],
    exclude = [],
    file

}) {
    const isInclude = include.some((reg: RegExp) => reg.test(file))
    const isExclude = exclude.some((reg: RegExp) => reg.test(file))

    if (!include.length && !exclude.length) {
        return true;
    }

    else if (!include.length) {
        return isExclude
    } else if (!exclude.length) {
        return isInclude
    } else {

        return isInclude && !isExclude
    }

}


export function isExternalModule(moduleName: string, externals: any, context = {}) {

    // 处理正则表达式形式
    if (externals instanceof RegExp) {
        return externals.test(moduleName);
    }

    // 处理对象形式
    if (typeof externals === 'object' && !Array.isArray(externals)) {
        return externals.hasOwnProperty(moduleName);
    }

    // 处理数组形式
    if (Array.isArray(externals)) {
        return externals.some(external => isExternalModule(moduleName, external, context));
    }

    // 处理字符串形式
    if (typeof externals === 'string') {
        return externals === moduleName;
    }

    // 处理函数形式
    if (typeof externals === 'function') {
        let isExternal = false;
        externals(context, moduleName, (err, result) => {
            if (result) isExternal = true;
        });
        return isExternal;
    }



    return false;
}



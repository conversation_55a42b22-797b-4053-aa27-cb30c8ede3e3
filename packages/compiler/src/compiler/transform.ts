// 转换wxml


import { XData } from "../types";
import {
    ElementNode,
    Node,
    RootNode
} from "./ast"

export type ITranformHandler = {
    transforms:Array<Function>,
    directive ? :XData
}


export function transform(node: Node,options:ITranformHandler) {



    let { transforms } = options


    let exits = []

    for (let i = 0 ; i < transforms.length;i++) {
        let transform = transforms[i];
        let exit  = transform(node,options)
         if (exit) exits.push(exit)
        
    }

    let { children } = node;
    
    if (children) {
        children.forEach((child:Node) => {
            transform(child,options)
        })
    }

   
    if (exits.length) {
        exits.forEach((ex) => ex())
    }

}

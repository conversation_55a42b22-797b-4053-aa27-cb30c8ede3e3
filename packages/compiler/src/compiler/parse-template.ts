

import { XData } from '../types'
import { NO, delimiters, isNil, relativeId, isString } from '../util'


import posthtml from "posthtml"

import { Parser } from "htmlparser2"
import {
    ImportType,
    ImportInfo,
    InterpolationNode,
    TemplateNode,
    NodeTypes,
    ElementNode,
    ElementTypes,
    AttributeNode,
    TextNode,
    CommentNode,
    AttributeConstantNode,
    RootNode
} from './ast'



import _ from "lodash"



const POST_HTML_OPTIONS = {
    recognizeNoValueAttribute: true,
    recognizeSelfClosing: true,
    closingSingleTag: 'slash',
    quoteStyle: 0 as const,
    replaceQuote: false,
    singleTags: ['switch', 'image', 'video', 'icon', 'progress', 'input'],
    sync: true
}


export interface ParseOptions {
    isIgnoreTag: (tag: string) => boolean,
    isTemplateTag: (params: any) => boolean,
    isIncludeTag: (params: any) => boolean,
    isComponentTag: (params: any) => boolean,
    request?: string
}


export type ParseContext = {
    code: string,
    options: ParseOptions,

    children: TemplateNode[],
    currentNode?: ElementNode | RootNode,
    stack: TemplateNode[],
    imports: ImportInfo[],
    methods: string[],
    data: string[],
    templateDefine?: XData,
    recordImports: (type: ImportType, props: XData) => unknown
    recordMethods: (name: string) => unknown
    reacordData: (name: string) => unknown
    recordTemplateDefine: (props) => unknown

    helper: (node: TemplateNode) => void
    start: (tag: string, props: AttributeNode[], unary: boolean) => unknown
    end: (tag: string) => unknown
    chars: (text: string) => unknown
    comment: (text: string) => unknown
}


var startTag = /^<([-A-Za-z0-9_]+)((?:\s+[a-zA-Z_:][-a-zA-Z0-9_:.]*(?:\s*=\s*(?:(?:"[^"]*")|(?:'[^']*')|[^>\s]+))?)*)\s*(\/?)>/,
    endTag = /^<\/([-A-Za-z0-9_]+)[^>]*>/,
    attr = /([a-zA-Z_:][-a-zA-Z0-9_:.]*)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|([^>\s]+)))?/g;


let defaultOptions: ParseOptions = {
    isIgnoreTag: NO,
    isIncludeTag: (tag: string, props: any[] = []) => tag === "include" && _.find(props, { key: "src" }),
    isTemplateTag: (tag: string, props: any[] = []) => tag === "template" && _.find(props, { key: "is" }),
    isComponentTag: () => false

}


export function createParseContext(template: string, options: ParseOptions): ParseContext {


    let context: ParseContext = {
        code: template,
        options,


        children: [],
        currentNode: undefined,
        stack: [],
        imports: [],
        methods: [],
        templateDefine: [], //当前文件的模版信息
        data: [],

        reacordData(name: string) {
            let { data } = context;
            if (data.includes(name)) return;
            data.push(name)
        },


        recordMethods(name: string) {
            let { methods } = context;
            if (methods.includes(name)) return;
            methods.push(name)
        },

        // 导入wxs的记录
        recordImports(type: ImportType, props: XData) {
            let { imports } = context;
            imports.push({
                type,
                props
            })
        },

        recordTemplateDefine(props: XData) {

            context.templateDefine.push(_.cloneDeep(props));
        },

        helper(node: TemplateNode) {
            if (!context.currentNode) {

                context.children.push(node)
            } else {
                context.currentNode.children?.push(node)
                node.parent = context.currentNode
            }

        },

        start(tag: string, props: AttributeNode[], unary: boolean) {
            if (options.isIgnoreTag(tag)) return;

            let tagType: ElementTypes = ElementTypes.ELEMENT;

            switch (tag) {

                case "include":
                    tagType = ElementTypes.INCLUDE;
                    break
                case "template":
                    tagType = ElementTypes.TEMPLATE;
                    break;

                case "import":
                    tagType = ElementTypes.IMPORT;
                    break;
                case "wxs":

                    tagType = ElementTypes.WXS;
                    break;

                default:
                    break;
            }

            let element: ElementNode = {
                type: NodeTypes.ELEMENT,
                tagType,
                tag,
                props,
                unary,
                children: [],
            };


            // 记录for变量
            if (props && props.length) {
                let hasFor = _.find(props, { dirname: "for" })

                if (hasFor) {
                    let itemName = _.find(props, { dirname: "for-item" })?.value ?? "item"
                    let indexName = _.find(props, { dirname: "for-index" })?.value ?? "index"

                    element.forInfo = {
                        itemName,
                        indexName,

                    }
                }
            }


            // 记录wxs
            if (tag === "wxs") {
                context.recordImports(ElementTypes.WXS, props)
            }

            if (tag === "import") {
                context.recordImports(ElementTypes.IMPORT, props)
            }

            if (tag === "include") {
                context.recordImports(ElementTypes.INCLUDE, props)
            }

            // template模版
            if (tag === "template" && _.find(props, { key: "name" })) {
                context.recordTemplateDefine(_.find(props, { key: "name" }));
            }


            context.helper(element)
            // 没有tag结束符
            if (!unary) {
                context.currentNode = element;
            }
        },

        end: function end(tag: string) {
            if (options.isIgnoreTag(tag)) return;

            if (!context.currentNode ||
                //@ts-ignore
                (context.currentNode.tag !== tag)) {
                const { request } = options;
                throw new Error(`${relativeId(request)} 格式错误，请检查`);

            }


            context.currentNode = context.currentNode.parent
        }
        ,

        chars: function chars(text: string) {

            let el: TextNode | InterpolationNode = {
                type: text && text.indexOf(delimiters[0]) !== -1 ? NodeTypes.INTERPOLATION : NodeTypes.TEXT,
                value: text,
                codegenNode: undefined
            };

            context.helper(el);
        },
        comment: function comment(text: string) {

            let el: CommentNode = {
                type: NodeTypes.COMMENT,
                value: text,
                parent: context.currentNode as ElementNode,
                codegenNode: undefined
            };

            context.helper(el);

        },
    }


    return context;

}


export function createParseRoot(context: ParseContext): RootNode {



    let root: RootNode = {
        type: NodeTypes.ROOT,
        children: context.children,
        imports: context.imports,
        methods: context.methods,
        templateDefine: context.templateDefine,
        codegenNode: undefined,
        components: [],
        data: context.data,
        code: context.code
    }

   

    context.children.forEach((child) => child.parent = root)

    return root;
}



export function baseParse(template: string, options: Partial<ParseOptions> = {}) {

    let mergedOptions = Object.assign(defaultOptions, options || {})
    let context = createParseContext(template, mergedOptions)


    parseHTML(context)


    return createParseRoot(context);



}




function parseHTML(context: ParseContext) {


    let html = context.code;

    const plugin = function () {
        return tree => {
    
           const walkTree = (nodes) => {

            for (let i = 0 ; i < nodes.length;i++) {
                const node = nodes[i]
           
                if (isString(node)) {
                    if ((node.trim()).startsWith("<!--")) {
                        context.comment(node)
                    } else {
                        context.chars(node)
                    }
                } else {

                    let { attrs = {}, tag, content = [] } = node;
                    var props: AttributeNode[] = [];

                    Object.keys(attrs).forEach((key) => {

                        let value = attrs[key];
                        if (value === true) value = undefined
                        if (!isNil(value)) value = value.replace(/\"/g, "'");
                        let policies = [
                            {

                                // wx:xxx = ""
                                match: (key: string, value: any) => {
                                    return /(?:^wx:([a-z0-9-]+))/i.exec(key)
                                },

                                exec: (key: string, value: any, res: any) => {
                                    return {
                                        type: NodeTypes.ATTRIBUTE_DIRECTIVE,
                                        dirname: res[1],
                                        key,
                                        value,
                                    }

                                }
                            },
                            // catch/bind
                            {
                                match: (key: string, value: any) => {

                                    return /^(catch:|catch|bind:|bind)([\w-]+)/.exec(key)
                                },

                                exec: (key: string, value: any, res) => {

                                    context.recordMethods(value);
                                    const iscatch = res[1].indexOf("catch") !== -1;
                                    return {
                                        type: NodeTypes.ATTRIBUTE_DIRECTIVE,
                                        dirname: "on",
                                        iscatch,
                                        key,
                                        eventName: res[2],
                                        value,
                                    }

                                }

                            },
                            // value = {{}}组件属性
                            {
                                match: (key: string, value: any) => {
                                    return value && value.indexOf(delimiters[0]) !== -1
                                },

                                exec: (key: string, value: any) => {

                                    return {
                                        type: NodeTypes.ATTRIBUTE_DIRECTIVE,
                                        dirname: key,
                                        key,
                                        value,
                                    }
                                }
                            },
                            // 常量属性
                            {
                                match: (key: string, value: any) => {

                                    return true
                                },

                                exec: (key: string, value: any) => {
                                    return {
                                        type: NodeTypes.ATTRIBUTE_CONSTANT,
                                        key,
                                        value,
                                    }

                                }
                            }
                        ]

                        for (let i = 0; i < policies.length; i++) {
                            let { match, exec } = policies[i]
                            let res = match(key, value)
                            if (res) {
                                let val = exec(key, value, res) as AttributeConstantNode
                                props.push(val)
                                break;
                            }
                        }

                       
                    })

                    context.start(tag, props, false);

                    walkTree(content);
                    context.end(tag);
                }
               
            }
           } 

           walkTree(tree)
          

            return tree
        }
    }

    posthtml()
        .use(plugin())
        .process(html, POST_HTML_OPTIONS)
    // while (html) {
    //     chars = true;

    //     advanceSpaces()

    //     // Comment
    //     if (html.indexOf('<!--') == 0) {
    //         index = html.indexOf('-->');

    //         if (index >= 0) {
    //             if (context.comment) context.comment(html.substring(4, index));
    //             advanceBy(index + 3);
    //             chars = false;
    //         }

    //         // end tag
    //     } else if (html.indexOf('</') == 0) {
    //         match = html.match(endTag);

    //         if (match) {
    //             advanceBy(match[0].length)
    //             //@ts-ignore
    //             match[0].replace(endTag, parseEndTag);
    //             chars = false;
    //         }

    //         // start tag
    //     } else if (html.indexOf('<') == 0) {
    //         match = html.match(startTag);

    //         if (match) {
    //             advanceBy(match[0].length)
    //             //@ts-ignore
    //             match[0].replace(startTag, parseStartTag);
    //             chars = false;
    //         }
    //     }

    //     if (chars) {
    //         index = html.indexOf('<');
    //         var text = '';
    //         while (index === 0) {
    //             text += '<';
    //             advanceBy(1)
    //             index = html.indexOf('<');
    //         }
    //         text += index < 0 ? html : html.substring(0, index);
    //         html = index < 0 ? '' : html.substring(index);

    //         if (context.chars) context.chars(text);
    //     }


    //     advanceSpaces()
    // }






    function parseStartTag(tag: string, tagName: string, rest: any, unary: boolean) {

    }

    //@ts-ignore
    function parseEndTag(tag, tagName: string) {
        if (context.end) context.end(tagName);
    }

    function advanceBy(numberOfCharacters: number): void {
        html = html.substring(numberOfCharacters)
    }

    function advanceSpaces(): void {
        const match = /^[\t\r\n\f ]+/.exec(html)
        if (match) {
            advanceBy(match[0].length)
        }

    }

}

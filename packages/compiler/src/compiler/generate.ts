



import {
    isString,
    isNil,
    isObject,
    isComponentFile,
} from "../util"

import {
    NodeTypes,
    RootNode,
    CodegenNode,
    IfBranchCodegenNode,
    ForBranchCodeGenNode,
    PropsCodegenNode,
    ElementCodegenNode,
    TextCodegenNode,
    CommentCodegenNode,
    CallCodegenNode,

} from "./ast"

import prettier from "@prettier/sync";

import {
    logger
} from "../logger"


import {
    nativeAttributes
} from "./rawHtmlAttributeKeys"

import _ from "lodash"
import { getUserOptions } from "../user-options";
import { isConditionTrue } from "../helper";




// 插入表达式
function insertExpression(str: string) {

    return "${" + str + "}"
}



function generateCodeMini(node: RootNode) {
    const children = node.children || [];
    //@ts-ignore
    const childrenContent = children.map((childAst) => generateCodeWechat(childAst)).join('\n');
    //@ts-ignore
    let props = node.props || [];

    props = props
        .map((item) => {
            const key = item.tra_key || item.key
            const value = item.tra_value || item.value
            if (isNil(item.value)) return `${key}`;
            else return `${key}="${value}"`;
        })
        .join(' ');
    props = props ? ` ${props}` : '';
    let type = node.type;
    if (type === NodeTypes.ROOT) {
        return childrenContent
    }
    else if (type === NodeTypes.ELEMENT) {
        //@ts-ignore
        let tag = node.tag;
        //@ts-ignore
        if (node.unary) {
            return `<${tag} ${props} />`;
        }
        return `<${tag} ${props}>${childrenContent}</${tag}>`;
    } else if (type === NodeTypes.TEXT || type === NodeTypes.INTERPOLATION) {
        //@ts-ignore
        return node.value;
    }
}


export function generateCodeAlipay(node: RootNode) {

    const code = generateCodeMini(node)
    return code;
  }


export function generateCodeWechat(node: RootNode) {
    const code = generateCodeMini(node)
    return code;
}



type IGnerateContext = {

    ast: RootNode,
    request: string,
    code: string,
    push: Function,
    nextline: Function,
    pushExpressionStart: Function,
    pushExpressionEnd: Function
}
function createCodeGenContext(node: RootNode, request: string) {

    let context: IGnerateContext = {
        request,
        ast: node,
        code: "",

        push(str: string) {
            context.code += str
        },

        nextline(num: number = 1) {
            while (num) {
                context.push('\n')
                num--
            }
        },
        pushExpressionStart() {
            context.push("${")
        },

        pushExpressionEnd() {
            context.push("}")
        }
    }

    return context;
}





function genNode(node: CodegenNode, context: IGnerateContext) {

    let { type } = node;

    let res: string = ""

    switch (type) {

        case NodeTypes.IF:

            res = genIfNode(node as IfBranchCodegenNode, context)
            break;

        case NodeTypes.FOR:
            res = genForNode(node as ForBranchCodeGenNode, context)
            break;
        case NodeTypes.PROPS:
            res = genNodeProps(node as PropsCodegenNode, context)
            break;
        case NodeTypes.ELEMENT:


        case NodeTypes.COMPONENT:
            res = genElementNode(node, context)
            break;

        case NodeTypes.INTERPOLATION:
        case NodeTypes.TEXT:
            res = genTextNode(node, context);
            break;

        case NodeTypes.COMMENT:

            res = genCommnent(node as CommentCodegenNode, context);

            break;

        case NodeTypes.CALL:

            res = genCall(node as CallCodegenNode, context);


    }

    return res;

}




function genNodeProps(node: PropsCodegenNode, context: IGnerateContext) {

    let { push } = context
    let { type, props = [] } = node;

    if (type !== NodeTypes.PROPS) return ""

    let str = "";

    props.forEach((prop) => {
        let { key, value} = prop

        let  value_str = value;
        if (value_str !== undefined) {
            str += ` ${key}="${value_str}" `
        }else {
            str += ` ${key} `
        }
        
       
    })

    return str

}


// 判断if 是不是在for里
function checkIsInForElse(node: IfBranchCodegenNode) {

    const { belongNode } = node;

    if (!belongNode) return;

    const { codegenNode } = belongNode


    const { type } = codegenNode

    if (type === NodeTypes.FOR) return true;
}

function genForNode(node: ForBranchCodeGenNode, context: IGnerateContext) {

    let { push, nextline, pushExpressionEnd, pushExpressionStart } = context
    let { list, item, itemName, indexName, belongNode } = node;
    
    const { type} = item

    let list_str = list;

    const itemStr = genNode(item, context);

    const expression = `processDomFor(${list_str},(${itemName},${indexName}) => {
         return ${type === NodeTypes.IF ? itemStr : "html`" + itemStr + "`" }
    })`


    return  insertExpression(expression)




}


function genCall(node: CallCodegenNode, context: IGnerateContext) {

    const { funcName, params ,thisArgs } = node

   
    
    let params_str = params || "";

    let res = thisArgs ? `${funcName}.call(${thisArgs},${params_str})` :  `${funcName}(${params_str})`

    return insertExpression(res)
}

function genIfNode(node: IfBranchCodegenNode, context: IGnerateContext) {

    let { condition, trueBranch, falseBranch } = node

    const condition_str = condition;


    const trueExpression = `html\` ${genNode(trueBranch, context)} \``;

    const falseExpression = falseBranch ? `html\` ${genNode(falseBranch, context)} \`` : "";

    let expression = ""
    if (falseBranch) {

        expression = `(${condition_str}) ? ${trueExpression} : ${falseExpression}`
    } else {
        expression = `(${condition_str}) ? ${trueExpression} : html\`\``
    }

    return checkIsInForElse(node) ? expression : insertExpression(expression)
}


function getCommentString(comment: string) {
    return `<!-- ${comment} -->`
}

function genCommnent(node: CommentCodegenNode, context: IGnerateContext) {

    return  (node.children || "") 



}

function genTextNode(node: CodegenNode, context: IGnerateContext) {
    const { push } = context
    const { type, children } = node as TextCodegenNode

    let res = ""
    // if (type === NodeTypes.TEXT) {
    //     res = children
    // } else if (type === NodeTypes.INTERPOLATION) {

    //     res = insertExpression(`${children}`)
    // }


    res = children

    return res;
}




function genElementNode(node: CodegenNode, context: IGnerateContext) {

    let { push } = context

    let { tag, children, propsCodegenNode,isRemoveSelf } = node as ElementCodegenNode;

    let res = ""

    if (isRemoveSelf) {
            res = genElementNodeParams(children, context)
    }else {
        res = `<${tag} `

        res += genElementNodeParams(propsCodegenNode, context);
        res += `>`
    
        children && (res += genElementNodeParams(children, context));
    
        res += `</${tag}>`
    }

       

    return res;

}


function genElementNodeParams(node: any, context: IGnerateContext) {

    if (!node) return "";

    let res = ""
    if (Array.isArray(node)) {
        node.forEach((n, index) => {
            res += genNode(n, context);

        })
    } else if (isObject(node)) {
        res = genNode(node as CodegenNode, context)
    } else {
        console.error(`what is this?`, node)
    }

    return res;

}




// 默认的函数
function genRender(context:IGnerateContext) {



    const { ast } = context;
    const { children } = ast


    let res = `export default function (_ctx) {
        ${ast.variableHosited.map((item) => item.expression).join(";")}
        return html\``
        
    const validChildren = children
    .filter((child) => {
        if (child.isRemoved) return false;
        if (child.ignoreDefaultRender) return false;
        return true;
    } );



  
   
    validChildren.forEach((child) => {
        res += genNode(child.codegenNode!, context);
        res += "\n"
    })

  

    res += "`\n}"


    return res

}



function genImports(context: IGnerateContext) {
    const { ast ,push} = context

    const { codeHelperInfo = {} } = ast

    const { wxs = [], imports = [] } = codeHelperInfo;

    push(`import { html  } from "lit";
          import { normalizeStyle , 
          normalizeClasses,
          processDomFor,
          
          processDomEventHandler , processDomDatasetHandler } from "@wosai/smart-mp-concise-runtime";
        `);

    wxs.forEach((item) => {

        const { localName , aliasPath } = item;
        context.push(`import ${localName} from "${aliasPath}"`)
        context.nextline();
    });


        
        imports.forEach((item) => {

            const { aliasPath , importLists = [] } = item
            context.push(`import  { ${importLists.map((item) => item.funcName).join(",\n")} } from "${aliasPath}"`)
            context.nextline();
        })
    


   

    return

}


function genFunctionMapVariable(context: IGnerateContext) {


    const { ast ,push} = context

    const { codeHelperInfo = {} } = ast

    // 模版的函数名称可能是动态的，需要处理
    const { imports = [] , templateDefines = [] } = codeHelperInfo;

    push(`
        
        const templateNameMap = {


        `)
    imports.forEach((item) => {

        const { importLists } = item;

        importLists.forEach((im) => {
            const { funcName,originName } = im;
            push(`"${originName}" : ${funcName},`)
        })

    })

    templateDefines.forEach((define) => {
        const {nameMap } = define;
        const { originName,funcName } = nameMap;
        push(`"${originName}" : ${funcName},`)
    })

    push(`};
        `)


    push(`function getTemplateFunction(name) {
        
            if (templateNameMap[name]) {

            return templateNameMap[name]
            }

            return function() {}
        
        }`)


}


function genWxsDefine(context: IGnerateContext) {

    const { ast,push } = context
    const { codeHelperInfo } = ast;

    const { wxsDefines = []} = codeHelperInfo;
    
    if (!wxsDefines.length) return ""


    wxsDefines.forEach((wxs) => {

        let { 
           
            code ,

        } = wxs;

        push(`
                ${code}
            
            `)

    })

}


function genTemplateDefine(context: IGnerateContext) {

    const { ast,push } = context
    const { codeHelperInfo } = ast;

    const { templateDefines = []} = codeHelperInfo;
    
    if (!templateDefines.length) return ""

    let res = ""


    templateDefines.forEach((define) => {
        const { node,nameMap } = define;

        const { funcName,originName } = nameMap;
        const { children } = node;
        const validChildren = children
        .filter((child) => !child.isRemoved );

        let templateRes = `export function ${funcName}(_ctx) {
            ${node.variableHosited.map((item) => item.expression).join(";")}
             return html\``
            validChildren.forEach((child) => {
                templateRes += genNode(child.codegenNode!, context);
                templateRes += "\n"
            })

        templateRes += "`\n}"

        res+=templateRes;
    })

   return res;


}


// style样式变化的参数
function genViewPort(context: IGnerateContext) {

    let { push ,request} = context
    const userOptions = getUserOptions();

    const { styleRule = [] } = userOptions;

  


    let matchedRule = {};
    for (let i = 0; i < styleRule.length;i++) {
            const rule = styleRule[i];
            const { include = [], exclude = []  } = rule
            if (isConditionTrue({ include, exclude , file : request})) {
                matchedRule = rule;
                break
            }
    }
    
    push(`const viewPortOptions = ${JSON.stringify(matchedRule)};\n`)

}


export function generateCodeWeb(node: RootNode, request: string) {


    let context = createCodeGenContext(node, request);

    genImports(context)

    genWxsDefine(context);

    genFunctionMapVariable(context)

    genViewPort(context);


    let renderCode = genTemplateDefine(context);

   

    renderCode += genRender(context);

    const totalCode = context.code + renderCode;

    const formatedCode = prettier.format(totalCode, {
        parser: 'babel',
        singleQuote: true,
        trailingComma: 'all',
        tabWidth: 2,
    });

   // const formatedCode = totalCode

   logger.debug(`formated is `,formatedCode)

    return {
        code: formatedCode,
        ast: node
    }

}


export const nativeAttributes = [
    // 全局属性
    'accesskey',
    'autocapitalize',
    'class',
    'contenteditable',
    'contextmenu',
    'dir',
    'draggable',
    'dropzone',
    'hidden',
    'id',
    'is',
    'itemid',
    'itemprop',
    'itemref',
    'itemscope',
    'itemtype',
    'lang',
    'part',
    'slot',
    'spellcheck',
    'style',
    'tabindex',
    'title',
    'translate',
  
    // 表单相关属性
    'accept',
    'accept-charset',
    'action',
    'autocomplete',
    'autofocus',
    'enctype',
    'form',
    'formaction',
    'formenctype',
    'formmethod',
    'formnovalidate',
    'formtarget',
    'height',
    'inputmode',
    'list',
    'max',
    'maxlength',
    'min',
    'multiple',
    'name',
    'novalidate',
    'pattern',
    'placeholder',
    'readonly',
    'required',
    'size',
    'src',
    'step',
    'type',
    'value',
    'width',
  
    // 其他元素特定的属性
    'alt',
    'async',
    'charset',
    'cite',
    'colspan',
    'content',
    'controls',
    'coords',
    'data-*', // data-前缀的自定义属性
    'datetime',
    'defer',
    'download',
    'for',
    'headers',
    'href',
    'hreflang',
    'http-equiv',
    'label',
    'lang',
    'loop',
    'low',
    'max',
    'media',
    'method',
    'min',
    'optimum',
    'poster',
    'preload',
    'rel',
    'rev',
    'reversed',
    'rows',
    'rowspan',
    'scope',
    'shape',
    'span',
    'src',
    'srcset',
    'start',
    'target',
    'type',
    'usemap',
    'value',
    'width',
    'wrap'
  ];
  
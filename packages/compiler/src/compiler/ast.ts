import { XData } from "../types"






export enum NodeTypes {
    ROOT,
    ELEMENT, // 元素节点
    TEMPLATEDEFINE,
    COMMENT, // 注释
    TEXT, // 明文本
    COMPONENT, //  自定义组件
    PROPS,   // 属性
    INTERPOLATION, // 文本的数据绑定
    ATTRIBUTE_DIRECTIVE, 
    ATTRIBUTE_CONSTANT,
    IF,
    FOR,
    CONSTPROP,
    DYNAMATICPROPS,
    EVENT,
    CALL
}

export enum ElementTypes {
    ELEMENT,
    COMPONENT,
    SLOT, // 使用slot
    TEMPLATE,// 使用template
    INCLUDE, // include节点
    IMPORT, // import 节点
    WXS , // wxs节点
}

export type Position = {
    column: number,
    line: number,
    offset: number,
}


export type Location = {
    start: Position,
    end: Position,
    source: string
}

export interface Node {
    type: NodeTypes,
    parent?: ElementNode | RootNode,
    codegenNode?: CodegenNode, // codegenNode, transform 的时候计算，用于生成代码
    children?: TemplateNode[] | undefined,
    isRemoved?: boolean // else if 分支，不参与codegen 
    ignoreDefaultRender?:boolean //  不参与默认导出函数
    concise_events ?:Array<any>
    datacId ? : string
}


// 元素节点
export interface ElementNode extends Node {
    type: NodeTypes.ELEMENT,
    tag: string,
    tagType: ElementTypes,
    children?: Array<TemplateNode>,
    props?: Array<AttributeNode>,
    unary?: boolean,
    forInfo?: forInfo | undefined
    variableHosited?:Array<any>,
    elementInfo?:XData
}

export type AttributeNode = AttributeConstantNode | AttributeDirectiveNode

export interface AttributeConstantNode extends Node {
    type: NodeTypes.ATTRIBUTE_CONSTANT
    key: string,
    value?: string
}

export interface AttributeDirectiveNode extends Node {
    type: NodeTypes.ATTRIBUTE_DIRECTIVE,
    dirname: string,
    bindname?:string, // 事件的名称
    key: string,
    value?: string
}



export interface InterpolationNode extends Node {
    type: NodeTypes.INTERPOLATION,
    value: string,
    codegenNode: TextCodegenNode | undefined
}

export interface CommentNode extends Node {
    type: NodeTypes.COMMENT,
    value: string,
    codegenNode: CommentCodegenNode | undefined
}

export interface TextNode extends Node {
    type: NodeTypes.TEXT,
    value: string,
    codegenNode: TextCodegenNode | undefined
}

export type forInfo = {
    itemName: string,
    indexName: string
}


export type TemplateNode =  TextNode | CommentNode | InterpolationNode | ElementNode



export type CodegenNode =
 ElementCodegenNode
    | TextCodegenNode 
    | CommentCodegenNode | IfBranchCodegenNode 
    | ForBranchCodeGenNode
    | ComponentCodegenNode
    | PropsCodegenNode
     | CallCodegenNode
     | CallCodegenNode

export type BaseCodegenNode = {
    type: NodeTypes,
    children?: CodegenNode[] | undefined | string ,
    tagKey?: string,
    belongNode?:Node

}


export interface ElementCodegenNode extends BaseCodegenNode {
    type: NodeTypes.ELEMENT | NodeTypes.TEMPLATEDEFINE,
    tag: string,
    propsCodegenNode?: PropsCodegenNode,
    isRemoved ? :boolean,
    isRemoveSelf ? :boolean
}

export interface CallCodegenNode extends BaseCodegenNode {

    type:NodeTypes.CALL,
    funcName:string,
    params?:string,
    thisArgs ? :string
}


export interface ComponentCodegenNode extends BaseCodegenNode {
    type: NodeTypes.COMPONENT,
    tag: string,
    propsCodeGenNode?: PropsCodegenNode,
    options:string, // 自定义组件导出的配置
}


export interface IfBranchCodegenNode extends BaseCodegenNode {
    type: NodeTypes.IF,
    condition: string,
    trueBranch: ElementCodegenNode,
    falseBranch?: IfBranchCodegenNode | ElementCodegenNode | ForBranchCodeGenNode | ComponentCodegenNode | CallCodegenNode
}

export interface ForBranchCodeGenNode extends BaseCodegenNode {
    type: NodeTypes.FOR,
    itemName: string,
    indexName: string,
    list: string,
    item: ElementCodegenNode | ComponentCodegenNode | IfBranchCodegenNode
}


export interface TextCodegenNode extends BaseCodegenNode {
    type: NodeTypes.TEXT | NodeTypes.INTERPOLATION,
    children: string
}

export interface CommentCodegenNode extends BaseCodegenNode {
    type: NodeTypes.COMMENT,
    children: string
}

export interface PropsCodegenNode  {
    type:NodeTypes.PROPS,
    props:CodeGenProp[],


}

export type CodeGenProp = {
    type:NodeTypes,
    subType?:NodeTypes,
    key: string,
    value: string | undefined | string,
    wrapFunc?:string
}

export enum stringType  {
    CONSTANT,
    DYNMATIC
}




export interface RootNode extends Node {
    type: NodeTypes.ROOT
    children: TemplateNode[]
    components: any[] // 引入的组件
    imports: ImportInfo[], // 引入wxs和template
    templateDefine ?:XData, // 如果是template 相关信息
    expressions ? :string[],
    methods: string[],
    data: string[],
    code:string , //原始code
    codeHelperInfo?:XData 
    variableHosited ? : Array<any>
}

export enum TagType {
    Start,
    End
}


export type ImportType = ElementTypes.IMPORT | ElementTypes.WXS | ElementTypes.INCLUDE;
// 导入的信息
export type ImportInfo = {
    type:ImportType,
    props:XData
}
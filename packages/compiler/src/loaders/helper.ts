
import { glob } from "glob";
import path from "path";
import { getContext, readAst, readFileSync , isWechat, isNpmModule } from "../util";
import JSON5 from "json5";

import template from "@babel/template";

import {
    LoaderContext
} from "webpack"
import { canHandleNodeModulesFiles, isExternalModule } from "../helper";
import { getUserOptions } from "../user-options";






export  function loadModule(this: LoaderContext<any>, file: string) {
    return new Promise((resolve, reject) => {
        if (!file) return resolve(null)
        this.loadModule(file, (err, result) => {
          if (err) return reject(err);
          resolve(result);
        });
      });
}


// 解析json里面有效的依赖，剔除外部依赖
export type GetDepOptions = {
    request?:string,
    code?:string,
    externals?:any
}

export type GetAppPagesOptions = GetDepOptions;


export function getAppJsonValidPages(options:GetDepOptions) {
    let { request , code  , externals =[]} = options;
    let appJson:any = code;
    if (request) {
        appJson = JSON5.parse(readFileSync(request));
    }else if (appJson) {
        appJson = JSON5.parse(appJson)
    }


    let pages = appJson.pages || [];

    const subPackages = appJson.subpackages || [];

    pages = pages.concat(
        subPackages.reduce((list, now) => {
            const root = now.root || ""
            const pages = (now.pages || []).map((page) => {
                return path.join(root, page)
            });;
            return list.concat(pages)

        }, [])
    )

    return pages.filter((page) => !isExternalModule(page,externals))


}

export function getComponentJsonGenericDependencies(options:GetDepOptions) {

        const { code  , externals} = options;

        const  jsonParsed = JSON5.parse(code);

        const { componentGenerics = {}  } = jsonParsed

        const map = {};

        const items = Object.values(componentGenerics);

        if (!items.length) return map;


        if(items.some((item) => { return typeof item === "boolean" })) {
            return map;
        }
        
        Object.keys(componentGenerics).forEach((key) => {
            const value = componentGenerics[key];
            const aliasPath = value.default;
            if (/plugin:\/\//.test(aliasPath)) return ;
            if (!isExternalModule(aliasPath,externals)) {
                map[key] = aliasPath;
        }
        })

        return map;

}

export function getComponentJsonValidDependencies(options:GetDepOptions) {

    let { request , code  , externals = [] } = options;
    let jsonParsed:any = code;
    if (request) {
        jsonParsed = JSON5.parse(readFileSync(request));
    }else if (jsonParsed) {
        jsonParsed = JSON5.parse(jsonParsed)
    }

    let importComponents = [];

    if (jsonParsed.usingComponents) {
        importComponents = jsonParsed.usingComponents || {}
    } 
    const map = {};
    Object.keys(importComponents).forEach((key:string) => {
        let item = importComponents[key];
        if (/plugin:\/\//.test(item)) return ;
        if (!isExternalModule(item,externals)) {
                map[key] = item;
        }
    })
       
    return map;
}

export type ParseDepOptions = {
    src:string,
    dep:string,
    resolver:any
}
export  function parseComponentJsonDependencies(options:ParseDepOptions) {

    const {src , dep,resolver } = options;
    const context = getContext(src);

    if (!dep.startsWith(".")) {
        const tryRelativePath = path.join(context,dep);
        const result = glob.sync(`${tryRelativePath}.{t,j}s`);
        if (result && result.length) return result[0]
    }
   

    let res =  resolver(context,dep)

    return res;



}


export function canAddMiniProgramRuntime(request:string) {
        const iswx = isWechat();
        const isNpmFile = isNpmModule(request);

        if (iswx) return false;

        if (isNpmFile && !canHandleNodeModulesFiles(request)) return false;
     
        return true;
}
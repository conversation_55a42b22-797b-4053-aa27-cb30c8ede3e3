


import {
    getFileExtName,
    isComponentFile,
    emitFile,
    getMiniFileName,
    isWechat,
    isAppFile
} from "../../util"

import JSON5 from "json5";



import { logger } from "../../logger";
import { getTargetRelativePathBySource, isExternalModule } from "../../helper";
import { getComponentJsonGenericDependencies, getComponentJsonValidDependencies, loadModule, parseComponentJsonDependencies } from "../helper";
import { LoaderContext } from "webpack";
import { createResolver } from "../../resolver";

let componentResolver : any

// merge request 
function mergePlatformItems(this:LoaderContext<any>,source:string,request:string) {

        const mergedKey = isWechat() ? "wx" :"my";

        const jsonParsed = JSON5.parse(source);

        if (jsonParsed[mergedKey])  {
            Object.assign(jsonParsed,{ ...jsonParsed[mergedKey]})
        }

        delete jsonParsed["wx"]
        delete jsonParsed["my"]

        return JSON.stringify(jsonParsed)

}



export async function transform(this:LoaderContext<any>,source:string,src:string) {
    const res = JSON5.parse(source);

    const usingComponents = res.usingComponents || {}

    const genericComponents = res.componentGenerics || {}

    const validJsonDependencies = getComponentJsonValidDependencies({
        code:source,
        externals:this._compiler.options.externals
    });


    const componentGenericDependencies = getComponentJsonGenericDependencies({
        code:source,
        externals:this._compiler.options.externals
    })



    function getDependencyPath(aliasPath:string) {

        if (aliasPath.startsWith("plugin://")) return ;

       let absolutePath = parseComponentJsonDependencies( { src , dep:aliasPath,resolver:componentResolver})

        if (absolutePath){
            const rel_path = getTargetRelativePathBySource(src,absolutePath);
            let ext = getFileExtName(rel_path);
            return rel_path.replace(ext,"");
        } 

    }
    

    // usingComponents依赖修改
    for (let name in validJsonDependencies) {
        let aliasPath = validJsonDependencies[name];
        const absPath = getDependencyPath(aliasPath);
        if (absPath) {
            usingComponents[name] = absPath;
        }
       
    }

    // 对componentsGeneric进行修改
    for (let name in componentGenericDependencies) {
        let aliasPath = componentGenericDependencies[name];
        const absPath = getDependencyPath(aliasPath);
        if (absPath) {
            genericComponents[name].default = absPath;
        }
       
    }


    return JSON.stringify(res)

}

export  async function processJsonFile(this:LoaderContext<any>,source: string) {

    const { _compiler:compiler,_module } = this;

    const { userRequest } = _module;
    
    // 目前只处理组件的json文件和app.json
    if (!isComponentFile(userRequest) && !isAppFile(userRequest)) return source

    if (!componentResolver) {
        componentResolver = createResolver({
            ...this._compiler.options.resolve,
            extensions:[".ts",".js"],
        })
       
    }


    let code = mergePlatformItems.call(this,source,userRequest);

     code = await transform.call(this,code,userRequest) as string;

    const distpath = this._compiler.options.output.path;

    // _module.concise_info = {
    //     code,
    //     emitAsset:({file}) => {

    //         emitFile(`${distpath}/${file}`,code);
    //     }
    // }

    const file = getMiniFileName(userRequest);

    emitFile(`${distpath}/${file}`,code);


    logger.debug("json is",code)

    return code


}
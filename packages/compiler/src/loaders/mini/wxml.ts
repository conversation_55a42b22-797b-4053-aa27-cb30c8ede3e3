

import {
    getContext,
    getRelativePath,
    isNil,
    readFileSync,
    emitFile,
    getMiniFileName
} from "../../util"

import {
    Node,
    RootNode,
    NodeTypes,
    ElementNode,
    ElementTypes,
    transform,
    generateCodeWechat,
    generateCodeAlipay,
    baseParse
} from "../../compiler"

import {
    createResolver
} from "../../resolver"

import {
    logger
} from "../../logger"

import {
    relativeId
} from "../../util"


//@ts-ignore
import _ from "lodash"

import {
    LoaderContext
} from "webpack"
import { getTargetRelativePathBySource } from "../../helper"
import { loadDependencies } from "./utils"
import { getUserOptions } from "../../user-options"


let dependencies = new Set();

let componentResolver: any


const processHandler = {

    wechat: {
        transform: createWechatProcessNode,
        generate: generateCodeWechat,
    },
    alipay: {
        transform: createAlipayProcessNode,
        generate: generateCodeAlipay,
    }
}


function convertYinhao(attrs: Array<any> = []) {
    if (!attrs) return;
    attrs.forEach((attr) => {
        if (!isNil(attr.value)) attr.value = attr.value.replace(/\"/g, "'");
    });
}


export function transformAliasPath(node: ElementNode , userRequest: string) {
    const context = getContext(userRequest)
    const { type } = node;
    if (type === NodeTypes.ELEMENT) {
        let { tagType, props } = node as ElementNode
        if (tagType === ElementTypes.IMPORT || tagType === ElementTypes.WXS || tagType === ElementTypes.INCLUDE) {
            let item = _.find(props, { key: "src" })
            if (item) {
                let aliasPath = item.value;
                const tra_value = componentResolver(context, aliasPath);

                if (tra_value) {
                    //@ts-ignore
                    item.tra_value = getTargetRelativePathBySource(userRequest, tra_value);
                    dependencies.add(tra_value)
                }
            } else {
                logger.error(`no src key with import/wxs in file ${relativeId(userRequest)}`)
            }

        }
    }


}

export function createWechatProcessNode(userRequest: string) {
    const context = getContext(userRequest)
    return (node: Node, rootNode: RootNode) => {
        // 替换依赖的路径，替换引号问题
        //@ts-ignore
            let { props, } = node
            transformAliasPath(node as ElementNode,userRequest)
            convertYinhao(props);
        
    }


}


export function convertWxs(node:ElementNode) {
  const { tag } = node;
  if (tag !== "wxs") return ;
  node.tag = 'import-sjs';
  let attrs = node.props;
  attrs.forEach((a) => {
    if (a.key === 'src') {
     //@ts-ignore
      a.tra_key = 'from';
    }
     //@ts-ignore
    if (a.key === 'module') a.tra_key = 'name';
  });
}


var attrList = {
    'wx:if': 'a:if',
    'wx:for': 'a:for',
    'wx:else': 'a:else',
    'wx:for-item': 'a:for-item',
    'wx:for-index': 'a:for-index',
    'wx:for-key': 'a:for-key',
    'wx:key': 'a:key',
    'wx:elif': 'a:elif',
  };

function convertAttrs(props = []) {
    if (!props) return;
    let attrMap = attrList;
    props.forEach((prop) => {
      let key = prop.key;
      if (attrMap[key]) {
        prop.tra_key = attrMap[key];
      }
      if (!isNil(prop.value)) prop.tra_value = prop.value.replace(/\"/g, "'");
    });
  }
  

  const platformTags = [
    "view",
    "button",
    "text",
  ]


function convertEventName({ tag, props = [] }) {
    let reg = /^((?:catch|bind):?)([a-zA-Z]+)/;
    props.forEach((attr) => {
      let name = attr.key;
      if (reg.test(name)) {
        attr.tra_key = name.replace(reg, function ($0, $1, $2) {
          let eventName = $2.replace(/\b(\w)(\w*)/g, function ($0, $1, $2) {
            return $1.toUpperCase() + $2;
          });
          if (platformTags.includes(tag)) return `catch${eventName}`;
          else return `on${eventName}`;
        });
      }
    });
  }
  

export function createAlipayProcessNode(userRequest: string) {
    return (node: Node, rootNode: RootNode) => {
        // 替换依赖的路径，替换引号问题
        //@ts-ignore
            let {props  } = node as ElementNode
            convertWxs(node as ElementNode)
            convertAttrs(props);
            convertEventName(node as ElementNode) 
            transformAliasPath(node as ElementNode,userRequest);
            convertYinhao(props);
        }

}





// 定义自定义的 wxml loader 函数
export async function processWxmlFile(this: LoaderContext<any>, source: string) {


    const { _module } = this;

    const userOptions = getUserOptions();
    const { target } = userOptions

    const { userRequest } = _module


    const rootNode = baseParse(readFileSync(userRequest), { request: userRequest });

    dependencies.clear();

    if (!componentResolver) {
        componentResolver = createResolver({
            ...this._compiler.options.resolve,
            extensions: [".wxml", ".wxs"]


        })
    }

    transform(rootNode, {
        transforms: [
            processHandler[target].transform(userRequest)

        ]

    })

    await loadDependencies.call(this, dependencies);


    const code = processHandler[target].generate(rootNode)

    const distpath = this._compiler.options.output.path;
    const file = getMiniFileName(userRequest);
    emitFile(`${distpath}/${file}`, code);

    return "";

};


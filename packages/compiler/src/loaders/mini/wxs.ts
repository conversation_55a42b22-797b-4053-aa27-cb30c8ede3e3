

const babelParser = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const babel = require('@babel/core');
import trabfowmWxsPlugin from './babel-transform-wxs-plugin'

import {
    XData
} from "../../types"

import {
    getTargetRelativePathBySource,
} from "../../helper"

import {
    emitFile,
    generateCodeByAst, getContext, getMiniFileName, isAlipay, readAst, readFileSync
} from "../../util"
import { createResolver } from "../../resolver";

import { loadModule } from "../helper";
import { loadDependencies } from "./utils";



let componentResolver : any
let dependencies = new Set();
function transform(code:string,src:string) {

 
    const ast = babelParser.parse(code, {
        sourceType: 'module',
        plugins: ['jsx'] // wxs 文件使用类 JavaScript 语法
    });



    traverse(ast, {
        CallExpression(path: XData) {
            const { callee, arguments: args } = path.node;
            if (callee.name === 'require' && args.length === 1) {
                const arg = args[0];
                const aliasPath = arg.value;

     
                const tra_value = componentResolver(getContext(src),aliasPath);
                if (tra_value) {
                    dependencies.add(tra_value)
                    arg.value = getTargetRelativePathBySource(src,tra_value);
                } 
                
            }
        }
    });


    return {
        ast,
        code : generateCodeByAst(ast)
    }

}

export  async function processWxsFile(source:string) {

    const { _module } = this;

    const { userRequest } = _module



    if (!componentResolver) {
        componentResolver = createResolver({
            ...this._compiler.options.resolve,
            extensions:[".wxs"],
        })
    }

    let code = source

     const ast = readAst(code)

     dependencies.clear();
     let {
        ast:trans_ast,
        code:trans_code
     } = transform(code,userRequest);


     await loadDependencies.call(this,dependencies)
    // // 转换 AST 并生成 ES5 代码
    let plugins = [];
    if (isAlipay()) {
       
        plugins.push(trabfowmWxsPlugin())
    }
    const result = babel.transformFromAstSync(trans_ast, trans_code, {
        presets: [
            [
                '@babel/preset-env',
                {
                  exclude: ['transform-typeof-symbol'], // 禁止转换 typeof
                },
              ],
        ], // 使用 preset-env 预设将 ES6 转换为 ES5
        plugins,
    }).code;
  

    // mod.code = result.code;

    const distpath = this._compiler.options.output.path;
    const file = getMiniFileName(userRequest);
    emitFile(`${distpath}/${file}`,result);

    return result;


}
//@ts-nocheck

var { types } = require('@babel/core');
var { check } = require('reserved-words');

export default  (options = {}) => {
  const state = {
    globals: new Set(),
    renamed: new Map(),
    identifiers: new Set(),
    isCJS: false,
  };

  const enter = (path) => {
    let cursor = path;

    // Find the closest function scope or parent.
    do {
      // Ignore block statements.
      if (types.isBlockStatement(cursor.scope.path)) {
        continue;
      }

      if (
        types.isFunction(cursor.scope.path) ||
        types.isProgram(cursor.scope.path)
      ) {
        break;
      }
    } while ((cursor = cursor.scope.path.parentPath));

    if (types.isProgram(cursor.scope.path)) {
      const nodes = [];
      const inner = [];

      // Break up the program, separate Nodes added by us from the nodes
      // created by the user.
      cursor.scope.path.node.body.filter((node) => {
        // Keep replaced nodes together, these will not be wrapped.
        if (node.__replaced) {
          nodes.push(node);
        } else {
          inner.push(node);
        }
      });

      const program = types.program([
        ...nodes,
        types.expressionStatement(
          types.callExpression(
            types.memberExpression(
              types.functionExpression(null, [], types.blockStatement(inner)),
              types.identifier('call')
            ),
            [types.identifier('module.exports')]
          )
        ),
      ]);

      cursor.scope.path.replaceWith(program);
      state.isCJS = true;
    }
  };

  return function () {
    return {
      post() {
        state.globals.clear();
        state.renamed.clear();
        state.identifiers.clear();
        state.isCJS = false;
      },

      visitor: {
        Program: {
          exit(path) {
            path.traverse({
              CallExpression: {
                exit(path) {
                  const { node } = path;

                  // Look for `require()` any renaming is assumed to be intentionally
                  // done to break state kind of check, so we won't look for aliases.
                  if (
                    !options.exportsOnly &&
                    types.isIdentifier(node.callee) &&
                    node.callee.name === 'require'
                  ) {
                    // Require must be global for us to consider this a CommonJS
                    // module.
                    state.isCJS = true;

                    // Check for nested string and template literals.
                    const isString = types.isStringLiteral(node.arguments[0]);
                    const isLiteral = types.isTemplateLiteral(
                      node.arguments[0]
                    );

                    // Normalize the string value, default to the standard string
                    // literal format of `{ value: "" }`.
                    let str = null;

                    if (isString) {
                      str = node.arguments[0];
                    } else if (isLiteral) {
                      str = {
                        value: node.arguments[0].quasis[0].value.raw,
                      };
                    } else if (options.synchronousImport) {
                      const str = node.arguments[0];
                      const newNode = types.expressionStatement(
                        types.callExpression(types.import(), [str])
                      );

                      // @ts-ignore
                      newNode.__replaced = true;

                      path.replaceWith(newNode);

                      return;
                    } else {
                      throw new Error(
                        `Invalid require signature: ${path.toString()}`
                      );
                    }

                    const specifiers = [];

                    // Convert to named import.
                    if (types.isObjectPattern(path.parentPath.node.id)) {
                      path.parentPath.node.id.properties.forEach((prop) => {
                        specifiers.push(
                          types.importSpecifier(prop.value, prop.key)
                        );

                        state.globals.add(prop.value.name);
                      });

                      const decl = types.importDeclaration(
                        specifiers,
                        types.stringLiteral(str.value)
                      );

                      // @ts-ignore
                      decl.__replaced = true;

                      path.scope
                        .getProgramParent()
                        .path.unshiftContainer('body', decl);
                      path.parentPath.remove();
                    }
                    // Convert to default import.
                    else if (str) {
                      const { parentPath } = path;
                      const { left } = parentPath.node;
                      // @ts-ignore
                      const oldId = !types.isMemberExpression(left)
                        ? left
                        : left.id;

                      // Default to the closest likely identifier.
                      let id = oldId;

                      // If we can't find an id, generate one from the import path.
                      if (
                        !oldId ||
                        !types.isProgram(parentPath.scope.path.type)
                      ) {
                        id = path.scope.generateUidIdentifier(str.value);
                      }
                      // Add state global name to the list.
                      state.globals.add(id.name);

                      // Create an import declaration.
                      const decl = types.importDeclaration(
                        [types.importDefaultSpecifier(id)],
                        types.stringLiteral(str.value)
                      );

                      // @ts-ignore
                      decl.__replaced = true;

                      // Push the declaration in the root scope.
                      path.scope
                        .getProgramParent()
                        .path.unshiftContainer('body', decl);

                      // If we needed to generate or the change the id, then make an
                      // assignment so the values stay in sync.
                      if (oldId && !types.isNodesEquivalent(oldId, id)) {
                        const newNode = types.expressionStatement(
                          types.assignmentExpression('=', oldId, id)
                        );

                        // @ts-ignore
                        newNode.__replaced = true;

                        path.parentPath.parentPath.replaceWith(newNode);
                      }
                      // If we generated a new identifier for state, replace the inline
                      // call with the variable.
                      else if (!oldId) {
                        path.replaceWith(id);
                      }
                      // Otherwise completely remove.
                      else {
                        path.parentPath.remove();
                      }
                    }
                  }
                },
              },
            });

            const programPath = path.scope.getProgramParent().path;

            // Even though we are pretty sure this isn't a CommonJS file, lets
            // do one last sanity check for an `import` or `export` in the
            // program path.
            if (!state.isCJS) {
              const lastImport = programPath
                .get('body')
                .filter((p) => p.isImportDeclaration())
                .pop();

              const lastExport = programPath
                .get('body')
                .filter((p) => p.isExportDeclaration())
                .pop();

              // Maybe it is a CJS file after-all.
              if (!lastImport && !lastExport) {
                state.isCJS = true;
              }
            }

            if (path.node.__replaced || !state.isCJS) {
              return;
            }

            const exportsAlias = types.variableDeclaration('var', [
              types.variableDeclarator(
                types.identifier('exports'),
                types.memberExpression(
                  types.identifier('module'),
                  types.identifier('exports')
                )
              ),
            ]);

            const moduleExportsAlias = types.variableDeclaration('var', [
              types.variableDeclarator(
                types.identifier('module'),
                types.objectExpression([
                  types.objectProperty(
                    types.identifier('exports'),
                    types.objectExpression([])
                  ),
                ])
              ),
            ]);

            // @ts-ignore
            exportsAlias.__replaced = true;
            // @ts-ignore
            moduleExportsAlias.__replaced = true;

            // Add the `module` and `exports` globals into the program body,
            // after the last `import` declaration.
            const lastImport = programPath
              .get('body')
              .filter((p) => p.isImportDeclaration())
              .pop();

            if (lastImport) {
              lastImport.insertAfter(exportsAlias);
              lastImport.insertAfter(moduleExportsAlias);
            } else {
              programPath.unshiftContainer('body', exportsAlias);
              programPath.unshiftContainer('body', moduleExportsAlias);
            }

            const defaultExport = types.exportDefaultDeclaration(
              types.memberExpression(
                types.identifier('module'),
                types.identifier('exports')
              )
            );

            path.node.__replaced = true;
            // @ts-ignore
            defaultExport.__replaced = true;

            programPath.pushContainer('body', defaultExport);
          },
        },

        ThisExpression: { enter },
        ReturnStatement: { enter },

        ImportSpecifier: {
          enter(path) {
            const { name } = path.node.local;

            // If state import was renamed, ensure the source reflects it.
            if (state.renamed.has(name)) {
              const oldName = types.identifier(name);
              const newName = types.identifier(state.renamed.get(name));

              path.replaceWith(types.importSpecifier(newName, oldName));
            }
          },
        },

        AssignmentExpression: {
          enter(path) {
            if (path.node.__ignore) {
              return;
            }

            path.node.__ignore = true;

            // Check for module.exports.
            if (types.isMemberExpression(path.node.left)) {
              const moduleBinding = path.scope.getBinding('module');
              const exportsBinding = path.scope.getBinding('exports');

              // Something like `module.exports.namedExport = true;`.
              if (
                types.isMemberExpression(path.node.left.object) &&
                path.node.left.object.object.name === 'module'
              ) {
                if (!moduleBinding) {
                  state.isCJS = true;
                  return;
                }
              } else if (
                types.isIdentifier(path.node.left.object) &&
                path.node.left.object.name === 'module'
              ) {
                if (!moduleBinding) {
                  state.isCJS = true;

                  // Looking at a re-exports, handled above.
                  if (types.isCallExpression(path.node.right)) {
                    return;
                  }
                }
              }
              // Check for regular exports
              else if (path.node.left.object.name === 'exports') {
                const { name } = path.node.left.property;
                if (
                  exportsBinding ||
                  // If export is named "default" leave as is.
                  // It is not possible to export "default" as a named export.
                  // e.g. `export.default = 'a'`
                  name === 'default'
                ) {
                  return;
                }

                state.isCJS = true;

                let prop = path.node.right;

                if (
                  (path.scope.getProgramParent().hasBinding(prop.name) ||
                    state.globals.has(prop.name)) &&
                  // Don't rename `undefined`.
                  prop.name !== 'undefined'
                ) {
                  prop = path.scope.generateUidIdentifier(prop.name);

                  const oldName = path.node.right.name;
                  state.renamed.set(oldName, prop.name);

                  // Add this new identifier into the globals and replace the
                  // right hand side with this replacement.
                  state.globals.add(prop.name);
                  path.get('right').replaceWith(prop);
                  path.scope.rename(oldName, prop.name);
                }

                // If we set an invalid name, then abort out.
                try {
                  // Ensure that the scope is clean before we inject new,
                  // potentially conflicting, variables.
                  const newName = path.scope.generateUidIdentifier(name).name;

                  path.scope.rename(name, newName);

                  // Check if this name is reserved, if so, then bail out.
                  if (check(name)) {
                    return;
                  }

                  const decl = types.exportNamedDeclaration(
                    types.variableDeclaration('let', [
                      types.variableDeclarator(
                        path.node.left.property,
                        types.memberExpression(
                          types.identifier('exports'),
                          path.node.left.property
                        )
                      ),
                    ]),
                    []
                  );

                  if (!state.identifiers.has(name)) {
                    path.scope
                      .getProgramParent()
                      .path.pushContainer('body', decl);
                    state.identifiers.add(name);
                  }
                } catch {}
              }
            }
          },
        },
      },
    };
  };
};

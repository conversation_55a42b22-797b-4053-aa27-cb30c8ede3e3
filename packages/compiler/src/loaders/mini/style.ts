

import { LoaderContext } from "webpack";
import {

    emitFile, getContext, getMiniFileName
} from "../../util"
import { createResolver } from "../../resolver";
import { getTargetRelativePathBySource } from "../../helper";
import { loadDependencies } from "./utils";

const postcss = require("postcss");
const postcssLess = require("postcss-less");


let dependencies = new Set();;
async function transform(this: LoaderContext<any>, source: string, userRequest: string) {

    const alias = this._compiler.options.resolve.alias;
    
    const resolver = createResolver({
        ...this._compiler.options.resolve,
        extensions: [".less", '.wxss'],
    })
    dependencies.clear();
    // 定义新的插件
    const replaceDependencyPlugin = {
        postcssPlugin: 'replaceDependency',
        AtRule: {
            import: (atRule) => {
                const importPath = atRule.params.replace(/['"]/g, ''); // 去除引号

                // 计算相对路径
                const absolutePath = resolver(getContext(userRequest), importPath);
                dependencies.add(absolutePath);
                const relativePath = getTargetRelativePathBySource(userRequest, absolutePath).replace(/\.[^/.]+$/, ''); // 去除后缀

                // 替换为相对路径
                atRule.params = `"${relativePath}"`;
            },
        },
    };

    // 将 `replaceDependencyPlugin` 添加到 PostCSS 插件列表中
    let res = await postcss([replaceDependencyPlugin]).process(source, {
        from: undefined,
        syntax: postcssLess,
    });

    return res.css;


}
// 最后给webpack用的
export async function processStyleFile(this: LoaderContext<any>, source: string) {


    const { _module } = this;

    const { userRequest } = _module;




    let code = await transform.call(this, source, userRequest);

    await loadDependencies.call(this, dependencies)

    const distpath = this._compiler.options.output.path;
    const file = getMiniFileName(userRequest);
    emitFile(`${distpath}/${file}`, code);

    return ""
}
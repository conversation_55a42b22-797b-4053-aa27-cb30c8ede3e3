
import {
    ensureMiniProgramRuntime,
} from "../../helper";
import {
    getComponentTemplateFile, getComponentJsonFile, getComponentStyleFile, 
     readAst, isAppFile, isComponentFile } from "../../util";

import {
    LoaderContext
} from "webpack"
const generate = require('@babel/generator').default;


import { logger } from "../../logger";



import { loadModule, canAddMiniProgramRuntime } from "../helper";




// load 其他文件
async function processComponentFiles(this: LoaderContext<any>, src: string) {
    const jsonFile = getComponentJsonFile(src);
    const styleFile = getComponentStyleFile(src);
    const wxmlFile = getComponentTemplateFile(src);

    await loadModule.call(this,jsonFile);
    await loadModule.call(this,styleFile);
    await loadModule.call(this,wxmlFile);
}

async function processAppFile(this: LoaderContext<any>,ast, src: string) {

    await processComponentFiles.call(this,src)


}


async function processComponentFile(ast, src: string) {

    await processComponentFiles.call(this,src);

}

export async function processScriptFile(this: LoaderContext<any>, source: string) {


    const { _module } = this;

    const { userRequest } = _module

    const code = source;


    let ast = readAst(code);

    if (canAddMiniProgramRuntime(userRequest)) {
        ensureMiniProgramRuntime(ast, userRequest);
    }


    if (isAppFile(userRequest)) {
        await processAppFile.call(this, ast, userRequest)
    } else if (isComponentFile(userRequest)) {
        await processComponentFile.call(this, ast, userRequest)
    }

    // 生成修改后的代码
    const output = generate(ast, {}, code).code;


    logger.debug("js is", output)
    return output
}
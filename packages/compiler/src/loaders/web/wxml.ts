import {
    baseParse, transform,
    generateCodeWeb,
    NodeTypes
} from "../../compiler";

import {
    LoaderContext
} from "webpack"



import {
    transformComment,
    createProcessElement,
    transformText,
    transformOn
} from "./web-wxml-transform"
import { processIfDefPlatformFile } from "../../helper";




export function processWxmlFile(this:LoaderContext<any>,source: string) {

    const { _module } = this;


    const { userRequest } = _module;


    const code = processIfDefPlatformFile(source,userRequest);

    const ast = baseParse(code,{request:userRequest});


    transform(ast, {
        transforms: [
            transformComment,
            createProcessElement.call(this,userRequest,ast),
            transformText
        ],
        directive:{
            on:transformOn
        }
        
    })


    checkRoot(ast)

    const codeWeb = generateCodeWeb(ast,userRequest).code;


    return codeWeb;
}


function checkRoot(node) {

    const { children = [] } = node;

    if (node.isRemoved) return;

    if (!node.codegenNode && node.type !== NodeTypes.ROOT) {
        console.log(`node is `,node)
        throw new Error(`child is null`)
    }

    children.forEach((child) => {
        checkRoot(child)
    })
}
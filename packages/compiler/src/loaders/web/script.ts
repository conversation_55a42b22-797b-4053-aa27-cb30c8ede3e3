
import { isAppFile,
     isComponentFile, 
     getComponentJsonFile,
     getComponentStyleFile, getComponentTemplateFile,
    readFileSync, readAst, fileIsExist, getTagName, getContext, isNpmModule } from "../../util";

import path from "path";

import JSON5 from "json5";
const generate = require('@babel/generator').default;


import template from "@babel/template";
import { logger } from "../../logger";
import { getAppJsonValidPages, getComponentJsonValidDependencies } from "../helper";
import { LoaderContext } from "webpack";

import _ from "lodash";
import { processIfDefPlatformFile } from "../../helper";
const t = require('@babel/types');


let miniPages = []

function isPage(src:string){

    const isPage = miniPages.some((page) => {
        const { dir, name } = path.parse(src)

        return page === `${dir}/${name}`

    })

    return isPage
}


function addImportNode(this: LoaderContext<any>, ast, src: string) {


    const styleFile = getComponentStyleFile(src);
    const jsonFile = getComponentJsonFile(src);
    const templateFile = getComponentTemplateFile(src)
    let jsonCode = processIfDefPlatformFile(readFileSync(jsonFile),jsonFile);
    const validJsonDependencies = getComponentJsonValidDependencies({
        code:jsonCode,
        externals:this._compiler.options.externals
    });
    

    let s = ` 
        import  { ${isPage(src) ? 'createPageComponent' : 'createWebComponent'} ,defineCustomElementIfNotDefined} from "@wosai/smart-mp-concise-runtime";
        import _json_ from "${jsonFile}";
        import concise_render from "${templateFile}";
        ${fileIsExist(styleFile) ? `import _styles_ from "${styleFile}"` : ' const _styles_ = ""'};

        ${Object.values(validJsonDependencies).

            map((item) => {
                return `import "${item}"`
            }).join("\n")}
        `

    const importTemplate = template`${s}`
    ast.program.body.unshift(importTemplate())
}


function addCustomElementDefine(ast, src: string) {

   

    const runtimeName = isPage(src) ? "createPageComponent" : "createWebComponent"

    const tagName = getTagName(src);
    const runTemplate = template`
            const _comp_ = ${runtimeName}({
                render : concise_render,
                styles:_styles_ ? _styles_.toString() :"",
                request:"${src}",
                componentPath:"${src.replace(/\.[^.]+$/, '')}",
                tag:"${tagName}",
                json:_json_
            
            });

            defineCustomElementIfNotDefined("${tagName}",_comp_);

    `

    //@ts-ignore
    ast.program.body.push(...runTemplate());

}



function processAppFile(this: LoaderContext<any>, ast, src: string) {

    const context = getContext(src);
    const appJsonFile = getComponentJsonFile(src);

    const styleFile = getComponentStyleFile(src);
    const appJson = JSON5.parse(readFileSync(appJsonFile));

    const { entryPagePath } = appJson
    let pages = getAppJsonValidPages({request:appJsonFile})

   

    // 入口页面
    if (entryPagePath) {

        const matchCondition = item => item === entryPagePath;

        const index = pages.findIndex(matchCondition);

        if (index > -1) {
            const [matchedItem] = pages.splice(index, 1); // 移除匹配项
            pages.unshift(matchedItem);                    // 插入到最前面
        }
    }

    miniPages = pages.map((page) => {
        return path.join(context, page)
    })


    const prevTemplate = template`
        import appJson from "${appJsonFile}";
        import { runApp } from "@wosai/smart-mp-concise-runtime";
        ${fileIsExist(styleFile) ? `import   "${styleFile}"` : ''}

        ${miniPages.map((page) => {

        return `import "${page}"`
    }).join("\n")}

        const pageMap = {
             ${pages.map((page) => {
        return `["${getTagName(path.join(context,page))}"] : "${page}"`
    }).join(",")}
        };


       
      `;


    const postTemplate = template`

            runApp({
                pages:pageMap,
                json:appJson,
                request:"${src}",
                 context:"${getContext(src)}"
            });

            export default {};
        `

    ast.program.body.unshift(...prevTemplate());
    ast.program.body.push(...postTemplate())

}


function processComponentFile(ast, src: string) {
    addImportNode.call(this, ast, src);
    addCustomElementDefine(ast, src);

}
export function processScriptFile(this: LoaderContext<any>, source: string) {


    const { _module } = this;

    const { userRequest } = _module

    let code = processIfDefPlatformFile(source,userRequest);



    let ast = readAst(code);


    if (isAppFile(userRequest)) {
        processAppFile.call(this, ast, userRequest)
    } else if (isComponentFile(userRequest)) {
        processComponentFile.call(this, ast, userRequest)
    } else {
        // 处理普通的js文件
    }





    // 生成修改后的代码
    const output = generate(ast, {}, code).code;
    logger.debug("code is ", output)
    return output
}
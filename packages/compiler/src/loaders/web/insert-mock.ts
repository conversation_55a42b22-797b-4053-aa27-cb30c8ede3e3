import fs from 'fs';
import path from 'path';
import { getUserOptions } from '../../user-options';
import { getRelativePath } from '../../util';


function getEntryFiles(compiler) {

    const results = [];
    const entries = compiler.options.entry;
    // 遍历 entry 对象
    Object.keys(entries).forEach((entryName) => {
      const entryValue = entries[entryName];

      // 检查是否有 import 数组
      if (entryValue && entryValue.import && Array.isArray(entryValue.import)) {
        const importArray = entryValue.import;

        // 解析入口文件路径
        importArray.forEach((entryFile, index) => {
          const fullEntryPath = path.resolve(compiler.context, entryFile);

          // 检查入口文件是否存在
          if (fs.existsSync(fullEntryPath)) {
            results.push(fullEntryPath)
          }
        });
      }
    });

    return results;
  }


export default function insertMockLoader(source) {
 // 获取当前文件的路径
 const filePath = this.resourcePath;
    const entries = getEntryFiles(this._compiler);
  // 判断当前文件是否为入口文件
  const isEntryFile = entries.includes(filePath)
  if (!isEntryFile) {
    return source;
  }


  const userConfig = getUserOptions();
  const apiMockFile = userConfig.apiMock;
  const requestMockFile = userConfig.requestMock;

 

  // Mock 文件的路径

  let modifiedSource = source;

  const addMockToFile = (mockFile:string) =>  {
    if (!mockFile) return ;
    if (!fs.existsSync(mockFile)) return ;

      const mockImport = `import '${getRelativePath(filePath,mockFile)}';`;
      modifiedSource = `${mockImport}\n${modifiedSource}`;
  }


  addMockToFile(apiMockFile);
  addMockToFile(requestMockFile);


  return modifiedSource
};

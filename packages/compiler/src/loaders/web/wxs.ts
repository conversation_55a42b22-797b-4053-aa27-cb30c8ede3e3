




import { LoaderContext } from "webpack";
import { readAst } from "../../util";
import { processIfDefPlatformFile } from "../../helper";

const t = require('@babel/types');
const babel = require('@babel/core');
const generate = require('@babel/generator').default;
const traverse = require('@babel/traverse').default;

const dataTypeKeys = [

    "Array",
    "Number",
    "String",
    "Boolean",
    "Object",
    "Function",
    "Date",
    "RegExp",
]


function addWxsGlobalVariable(this:LoaderContext<any>,source:string) {

    //@ts-ignore
    if (this._module.hasAddWxsGlobalVariabale) return ;
 //@ts-ignore
    this._module.hasAddWxsGlobalVariabale = true;

    const getDate = `const getDate = function(params) { 
                                        return new Date(params) 
                                    };`
    const getRegExp = `const getRegExp = function (reg,flag) { 
                                            return new RegExp(reg,flag) 
                                        };`

    return `${getDate}${getRegExp}${source}`
}

function processDataTypeCheck(source: string) {

    const ast = readAst(source);

    traverse(ast, {
        BinaryExpression(path) {
            const { left, right, operator } = path.node;

            if (!(operator === '===' || operator === "==")) return;


            [[left, right], [right, left]].forEach((item) => {

                const [type, raw] = item;

                if (t.isStringLiteral(type) && dataTypeKeys.includes(type.value)
                    && t.isMemberExpression(raw) && t.isIdentifier(raw.property, { name: 'constructor' })) {
                    const direction = t.isStringLiteral(path.node.left) ? "left" :"right"
                    path.node[direction] = t.identifier(type.value);
                }
            })


        }
    });

   let transformedCode = generate(ast).code;

    return transformedCode;

}

export function processWxsFile(this:LoaderContext<any>,source: string) {

    let _source = processIfDefPlatformFile(source,this._module.userRequest);
    _source = addWxsGlobalVariable.call(this,_source);
    const code = processDataTypeCheck.call(this,_source)

    return code;


}
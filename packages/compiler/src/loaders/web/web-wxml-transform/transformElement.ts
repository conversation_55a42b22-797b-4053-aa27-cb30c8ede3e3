
//@ts-ignore
import { LoaderContext } from "webpack";
import {
    Node,
    PropsCodegenNode,
    ElementNode,
    CodeGenProp,
    AttributeDirectiveNode,
    ITranformHandler,
    ElementCodegenNode,
    ForBranchCodeGenNode,
    IfBranchCodegenNode,
    NodeTypes,
    RootNode,
    AttributeNode,
    CallCodegenNode,
    baseParse,
    ElementTypes
} from "../../../compiler/index"

import {
    getTemplateFunctionName,
    generateUniqueCode,
    deleteBrackets, getContext, generateId,
    getTagName, isComponentFile, isString, readFileSync, relativeId, mapTags, getComponentJsonFile, readAst
} from "../../../util";

import {
    createResolver
} from "../../../resolver"

import {
    getSblingConditionNode,
    attachChildrenCodegen,
    getExpMatches,
    findTemplateDefineNode,
    processEventsCodeGen,
} from "./helper"
import {
    processExpression,
    transformExpression
} from "./transformExpression"

import JSON5 from "json5";


import _ from "lodash";

import { getComponentJsonValidDependencies,parseComponentJsonDependencies } from "../../helper";
import { processIfDefPlatformFile } from "../../../helper";


let componentResolver: any




// 获取元素的信息
function getElementInfo(this: LoaderContext<any>, tag: string, request: string, node) {

    let info = {
        tag,
        customComponent: false
    }


    if (mapTags.includes(tag)) {
        info.tag = `concise-${tag}`;
        return info;
    }


    if (!isComponentFile(request)) return info;

    const jsonFile = getComponentJsonFile(request)

    let jsonCode = processIfDefPlatformFile(readFileSync(jsonFile),jsonFile);
    const jsonParsed = JSON5.parse(jsonCode);



    const componentPlaceholder = jsonParsed["componentPlaceholder"] || {}

    if (componentPlaceholder[tag]) { // 占位符号，再找
        return getElementInfo.call(this, componentPlaceholder[tag], request, node)
    }

   

    const valiidComponents = getComponentJsonValidDependencies({ code: jsonCode, externals: this._compiler.options.externals });


    let value = valiidComponents[tag]
    if (!value) return info // 不是自定义的


    value = parseComponentJsonDependencies({ src: jsonFile, dep: value, resolver: componentResolver })


    const web_tag_name = getTagName(value);
    info.tag = web_tag_name
    info.customComponent = true;


    // 自定义组件，添加concise_id 
    if (!node.props.find((prop) => prop.key === "src-path")) {
        node.props.push({
            type: NodeTypes.ATTRIBUTE_CONSTANT,

            key: "src-path",
            value: relativeId(value)
        })
    }

    if (!node.props.find((prop) => prop.key === "parentid")) {
    node.props.push({
        type: NodeTypes.ATTRIBUTE_CONSTANT,

        key: "parentid",
            value: "${this.concise_id}"
        })
    }


    return info

}




function processWxsDefine(node,props: AttributeNode[], root: RootNode, request: string) {

    let wxsModuleDefine = _.find(props,{ key :"module"});
    if (!wxsModuleDefine) {
        throw new Error(`wxs has no module prop in file ${relativeId(request)}`)
    }

    const moduleName = wxsModuleDefine.value;

    const code = node.children.map((child) => child.value).join("\n");

   
  // 3. 生成修改后的代码
  const outputCode = `var ${moduleName} = (function(){
  
        var module = { exports :() => {}};

        ${code}

        return module.exports
  })();\n`


  let { codeHelperInfo } = root;

  if (!codeHelperInfo.wxsDefines) codeHelperInfo.wxsDefines = [];

  codeHelperInfo.wxsDefines.push({
      localName:moduleName,
      code : outputCode,
      node
  })

  node.isRemoved = true;


}

function processWxsInclude(node,props: AttributeNode[], root: RootNode, request: string) {

    let wxsAliasPath = _.find(props, { key: "src" })
   
    if (!wxsAliasPath) {

        let wxsModuleDefine = _.find(props,{ key :"module"});
        // wxml本文件定义了wxs
        if (wxsModuleDefine) {

            return processWxsDefine(node,props,root,request)
        }
        throw new Error(`wxs has no src prop in file ${relativeId(request)}`)
    }

    let wxsAliasName = _.find(props, { key: "module" })

    if (!wxsAliasName) {
        throw new Error(`wxs has no module  prop in file ${relativeId(request)}`)
    }


    const { codeHelperInfo } = root;

    if (!codeHelperInfo.wxs) codeHelperInfo.wxs = [];



    codeHelperInfo.wxs.push({
        aliasPath: wxsAliasPath.value, // 目标的相对地址
        localName: wxsAliasName.value,// 导入的名称
        isDefault: true
    })


}


let modulesMap = new Map();

//<import src = "./a.wxml">
// 引用了模版
function processImport(this: LoaderContext<any>, props: AttributeNode[], root: RootNode, request: string) {

    let aliasPath = _.find(props, { key: "src" })
    if (!aliasPath) {
        throw new Error(`import tag has no src prop in file ${relativeId(request)}`)
    }

    //@ts-ignore
    aliasPath = aliasPath.value;


    const targetPath = componentResolver(getContext(request), aliasPath);

    if (!modulesMap.has(targetPath)) {
        const ast = baseParse(readFileSync(targetPath));
        modulesMap.set(targetPath, { ast })
    }

    let targetAst = modulesMap.get(targetPath).ast;
    const targetTemplateDefine = targetAst.templateDefine;


    const { codeHelperInfo } = root;

    if (!codeHelperInfo.imports) codeHelperInfo.imports = [];

    const len = codeHelperInfo.imports.length;


    codeHelperInfo.imports.push({
        aliasPath: aliasPath,
        importLists: targetTemplateDefine.map((item) => {
            return {
                funcName:getTemplateFunctionName(item.value),
                originName:item.value
            };
        }),
        isDefault: false,
    })


}


//<include src = "./a.wxml">

function processInclude(this: LoaderContext<any>, node: ElementNode, root: RootNode, request: string) {

    const { props } = node
    let aliasPath = _.find(props, { key: "src" })
    if (!aliasPath) {
        throw new Error(`include tag has no src prop in file ${relativeId(request)}`)
    }

    //@ts-ignore
    aliasPath = aliasPath.value;
    const targetPath = componentResolver(getContext(request), aliasPath);

    if (!modulesMap.has(targetPath)) {
        const ast = baseParse(readFileSync(targetPath));
        modulesMap.set(targetPath, { ast })
    }

    let targetAst = modulesMap.get(targetPath).ast;

    const validChildren = targetAst.children.filter((child) => {

        return child && child.tag !== "wxs" && child.tag !== "template"
    })
    // 循环处理里面的include
    const walkTree = (tree) => {

        for (let i = 0; i < tree.length; i++) {

            const node = tree[i]

            let { children = [] } = node

            if (node && node.tag === "include") {
                processInclude.call(this, node, root, targetPath)
            }

            walkTree(children)

        }

    }

    walkTree(validChildren)


    // 修改node，重新处理
    node.tag = "block";
    node.props = props.filter((prop) => { return prop.key !== "src" });
    node.unary = false;
    node.children = validChildren;
    node.tagType = ElementTypes.ELEMENT

    node.children.forEach((child) => {
        child.parent = node;
    })

    return;


}


// 模版调用

function generateTemplateCallCodeGen(node: ElementNode, root: RootNode, request: string , propsCodegenNode): CallCodegenNode {

    const { props } = node;

    let properties = propsCodegenNode.props;

    let  funcNameExpressionItem = _.find(properties, { key: "is" }) || _.find(properties, { key: ".is" });

    if (!funcNameExpressionItem) {
        throw new Error(`template tag has no is prop in file ${relativeId(request)}`)
    }

    let funcNameExpression = funcNameExpressionItem.value;



    let data = _.find(props, { key: "data" })?.value
    let tran_str = ""

    if (data) {

       
         const str = deleteBrackets(data)

         const prefix = `const a = `;

         tran_str = processExpression(`${prefix}{${str}}`, node, root)
       
         tran_str =tran_str.match(/\{[\s\S]*\}/)[0]


    }


    return {

        type: NodeTypes.CALL,
        funcName: `getTemplateFunction(\`${funcNameExpression}\`)`,
        thisArgs: "this",
        params: `${tran_str}`
    }



}


// 模版定义
function processTemplateDefine(node: ElementNode, root: RootNode, request: string) {

    const { props } = node;
    node.variableHosited = [];
    let  originName = _.find(props, { key: "name" }).value;
    let funcName = getTemplateFunctionName(originName);
    const { codeHelperInfo } = root;

    if (!codeHelperInfo.templateDefines) codeHelperInfo.templateDefines = [];

    codeHelperInfo.templateDefines.push({

        nameMap: {
            funcName, // 模版函数名称
            originName, // 模版定义的name,
        },
        isDefault: false,
        node
    })

}

// 作为element的属性，不作为js的属性

function postTransformPropsKey({
    key,
    value, // 处理过的值
    node,

}) {

    // 作为html属性的，肯定就是字符串
    const htmlPropertiesFilterKeys = [
        "class", "style", "id",
        (key: string) => key.startsWith("data-"),
        () =>  !node.elemntInfo.customComponent,
        () => value.indexOf("${") === -1,
    ]

    let isHtmlPropKey = htmlPropertiesFilterKeys.some((item) => {

        if (typeof item === "function") return item(key)
        else return item === key

    })

    if (node.tag === "rich-text" && key === "nodes") {
        isHtmlPropKey = false

    }


    return isHtmlPropKey ? key : `.${key}`
}



function postTransformPropsValue({
    key, value, node
}) {


    const policies = [
        {
            match: () => {
                let { tag } = node;
                return (tag === "textarea" && key === "placeholder-style") || key === "style"
            },
            done: () => {
                return `\${normalizeStyle(\`${value}\`,viewPortOptions)}`
            }
        }, 
        {
            match: () => {
                return key === "class"
            },

            done: () => {
                if (!value) return value;
           
                return `\${normalizeClasses.call(this,\`${value}\`)}`
            }
        },
        
        {

            match: () => {
                return key.startsWith("data-")
            },

            done: () => {
                /**
                 * {{0}} -> "0"
                 * "1" - >"1"
                 * "data" -> "${_ctx.data}"
                 * 
                 */


                // 提取${}表达式
                function extractContent(str) {
                    const match = str.match(/\${(.*?)}/);
                    return match ? match[1] : "'" + str + "'";
                };


                const params = extractContent(value);
                return `\${processDomDatasetHandler(${params})}`

            }
        }
    ]

    for (let i = 0; i < policies.length; i++) {
        const item = policies[i];
        const { match, done } = item;
        if (match()) {
            return done()
        }
    }

    return value;


}



export function createProcessElement(this: LoaderContext<any>, request: string, root: RootNode) {


    if (!componentResolver) {
        componentResolver = createResolver({

            ...this._compiler.options.resolve,
            extensions: [".wxml", ".wxs"],
        })
    }

    const processNode = (node: Node, options: ITranformHandler) => {

        const { type } = node;

        if (type === NodeTypes.ROOT) {
            //@ts-ignore
            node.codeHelperInfo = {}

            // @ts-ignore
            node.expressions = []; // 动态表达式的定义

            // @ts-ignore
            node.variableHosited = [];// 变量提升

            node.datacId = generateUniqueCode();

        }

        if (type !== NodeTypes.ELEMENT) return;

        const { tag, props, forInfo } = node as ElementNode;

        let hasIf: string;
        let hasElsIf: string;
        let hasElse = false;
        let hasFor: string;
        let itemName = forInfo ? forInfo.itemName : "";
        let indexName = forInfo ? forInfo.indexName : "";


        const isTemplateDefine = tag === "template" && _.find(props, { key: "name" })
        const isTemplateCall = _.find(props, { key: "is" })


        if (tag === "wxs") {
            processWxsInclude(node,props, root, request);
            node.isRemoved = true;
            return;
        }

        // import 模版
        if (tag === "import") {
            processImport.call(this, props, root, request);
            node.isRemoved = true;
            return;
        }

        if (tag === "include") {
            processInclude.call(this, node as ElementNode, root, request);

            return processNode.call(this, node, options) // 重新处理
        }

        // // 引用了模版
        // if (tag === "template" && isTemplateCall) {
        //     processTemplateCall(node as ElementNode,root,request);
        //     return ;
        // }

        if (isTemplateDefine) {
            processTemplateDefine(node as ElementNode, root, request);
        }


        const elemntInfo = getElementInfo.call(this, tag, request, node);
        //@ts-ignore
        node.elemntInfo = elemntInfo;



        const propsCodegenNode = buildProps();


        const templateCallCodegenNode: CallCodegenNode = createTemplateCallCodegenNode();
        const elementCodegenNode: ElementCodegenNode = createElementCodegen();
        const ifCodegenNode = createIfCodeGenNode();
        const forCodegenNode = createForCodegenNode();



        const currentCodegenNode = getCurrentCodeGen();


        if (hasElsIf || hasElse) {
            //@ts-ignore
            let prevNode = getSblingConditionNode(node as ElementNode, node); // 获取条件语句
            if (!prevNode) throw new Error("wx:else:if has no if branch");

            (prevNode!.codegenNode as IfBranchCodegenNode).falseBranch = currentCodegenNode;
            node.isRemoved = true;

        }

        if (tag === "block" || isTemplateDefine) {
            elementCodegenNode.isRemoveSelf = true
        }
        if (isTemplateDefine) {
            node.ignoreDefaultRender = true
        }

        node.codegenNode = currentCodegenNode;




        return () => {

            attachChildrenCodegen(node as ElementNode, elementCodegenNode);

            processEventsCodeGen(node as ElementNode, elementCodegenNode)
        };

        function getCurrentCodeGen() {
            return forCodegenNode || ifCodegenNode || templateCallCodegenNode || elementCodegenNode
        }


        function createTemplateCallCodegenNode() {

            if (!isTemplateCall) return;

            return generateTemplateCallCodeGen(node as ElementNode, root, request , propsCodegenNode)

        }

        // 属性的代码创建

        function createForCodegenNode(): ForBranchCodeGenNode | undefined {

            if (!hasFor) return;

            return {
                type: NodeTypes.FOR,
                itemName,
                indexName,
                list: hasFor,
                item: ifCodegenNode || elementCodegenNode,
                belongNode: node,
            }
        }


        function createIfCodeGenNode(): IfBranchCodegenNode | undefined {

            if (hasIf || hasElsIf) {

                return {
                    type: NodeTypes.IF,
                    condition: hasIf || hasElsIf,
                    belongNode: node,
                    //@ts-ignore
                    trueBranch: templateCallCodegenNode || elementCodegenNode
                }
            }
        }


        function createElementCodegen(): ElementCodegenNode {

            let elementCodegenNode: ElementCodegenNode = {
                type: NodeTypes.ELEMENT,
                //@ts-ignore
                tag: `${elemntInfo.tag}`,
                propsCodegenNode,
            }


            return elementCodegenNode
        }

        function buildProps(): PropsCodegenNode | undefined {

            //@ts-ignore
            let propcodegenNode: PropsCodegenNode = {}

            let {
                props = [],
            } = node as ElementNode

            if (!props.length) return;
            let properties: CodeGenProp[] = [];


            // 对自定义属性增加一份值
            function postProcessProperties({
                postKey,
                postValue,
                key }) {

                if (!elemntInfo.customComponent) return;

                // js的属性custom-style 再复制份成customStyle
                if (postKey.startsWith(".")) {
                    if (/^[a-z]+(-[a-z]+)+$/.test(key) && !props.some((prop) => prop.key === _.camelCase(key))) {
                        properties.push({
                            type: NodeTypes.DYNAMATICPROPS,
                            key: `.${_.camelCase(key)}`,// a-b ->aA
                            value: postValue
                        });

                    }
                } else {
                    // html属性,
                    // aA -> 
                    let kebKey = _.kebabCase(key);
                    if (kebKey === key) return ;
                    if (props.some((prop) => prop.key === kebKey)) return;

                    if (kebKey !== key) {
                        properties.push({
                            type: NodeTypes.CONSTPROP,
                            key: kebKey,
                            value: postValue
                        });
                    }

                }

            }

            for (let i = 0; i < props?.length; i++) {

                let prop = props[i];

                //@ts-ignore
                let { type, key, value, dirname } = prop

                let transvalue: string
                //@ts-ignore
                let directiveProcessor = options?.directive[dirname];


                // 当前有slot属性，但是父节点不是自定义组件，这个slot使用不是标准的用法，则不产出
                if (key === "slot") {
                    // @ts-ignore
                    if (node.parent && mapTags.includes(node.parent.tag)) {
                        continue;
                    }
                }

                if (type === NodeTypes.ATTRIBUTE_CONSTANT) {
                    transvalue = transformExpression({
                        // prefix:"prop",
                        node,
                        root,
                        exp: value,

                        // rpx->px
                        // 属性：独立表达式自动加${}，常量自动增加引号
                    });

                    let postValue = postTransformPropsValue({
                        key,
                        value: transvalue,

                        node
                    });
                    properties.push({
                        type: NodeTypes.CONSTPROP,
                        key: key, // 不能用.key形式，不然传表达式了,不写值是true，需要特殊处理
                        value: postValue,

                    })

                    postProcessProperties({
                        postKey:key,key,postValue
                    })
                } else {


                    if (dirname === "if") {
                        // wx:if = "name && name.data" -> ctx.name && ctx.name.data
                        transvalue = transformExpression({
                            // prefix:"prop",
                            node,
                            root,
                            exp: value,

                        });

                        hasIf = getExpMatches(value) ? transvalue : '"' + transvalue + '"'

                        continue;
                    }

                    if (dirname === "elif") {
                        transvalue = transformExpression({
                            // prefix:"prop",
                            node,
                            root,
                            exp: value,

                            // 属性：独立表达式，常量自动增加引号
                        });

                        hasElsIf = getExpMatches(value) ? transvalue : '"' + transvalue + '"'

                        continue;
                    }


                    if (dirname === "else") {
                        hasElse = true;
                        continue;
                    }


                    if (dirname === "for") {
                        transvalue = transformExpression({
                            // prefix:"prop",
                            node,
                            root,
                            exp: value,

                        });
                        hasFor = getExpMatches(value) ? transvalue : JSON.stringify(transvalue.split(""))
                        continue
                    }

                    if (key === "wx:for-item" ||
                        key === "wx:for-index"

                    ) {

                        continue;
                    }

                    if (isTemplateCall && key === "data") continue; // 微信这傻逼，data 的表达式什么乱七八糟的，不是合法的语句

                    if (directiveProcessor) {
                        let res = directiveProcessor(prop, node)
                        if (res)
                            properties.push(res)
                    } else {

                        // 含有多个表达式
                        // class = "name {{age}}" -> `name ${ctx.age}
                        transvalue = transformExpression({

                            node,
                            root,
                            exp: value,
                            isReplaceExpression: true,

                            // rpx->px
                            // 属性：独立表达式自动加${}，常量自动增加引号
                        });


                        const postKey = postTransformPropsKey({ key, node, value: transvalue });
                        const postValue = postTransformPropsValue({ key, value: transvalue, node });

                        properties.push({
                            type: NodeTypes.DYNAMATICPROPS,
                            key: postKey,
                            value: postValue

                        })

                        postProcessProperties({postKey,key,postValue})


                    }
                }

            }

            if (!properties.length) return;

            propcodegenNode = {
                props: properties,
                type: NodeTypes.PROPS,
            }


            return propcodegenNode;
        }

    }

    return processNode
}

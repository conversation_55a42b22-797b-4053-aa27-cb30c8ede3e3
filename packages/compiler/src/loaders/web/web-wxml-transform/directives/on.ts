
import { XData } from "../../../../types";
import {
    Node,
    NodeTypes,
    TemplateNode
} from "../../../../compiler"

import {
    getExpMatches,
    getRootNode
} from "../helper"

import {
    transformExpression
} from "../transformExpression"


// catchtap = "onClick" -> "onclick"

// catchtap = "{{expresiion}}" -> "ctx.expression"
//

export function transformOn(prop:XData,node:Node) {


        
        let { value,key , eventName , iscatch } = prop;

        if (!value) return 
        let exp = transformExpression({
           // prefix:"eventFunc",
            node,
            root:getRootNode(node as TemplateNode),
            exp:value,
        }) as string

       
         exp = getExpMatches(value) ? exp :  "'" + exp+ "'";

        return  {
            //@ts-ignore
            type:NodeTypes.CONSTPROP,
            subType: NodeTypes.EVENT,
            iscatch ,
            key : eventName,
            value :  exp
        }

}
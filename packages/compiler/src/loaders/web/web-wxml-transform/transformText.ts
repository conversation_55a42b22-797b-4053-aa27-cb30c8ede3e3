
import {
    NodeTypes,
    TextNode,
    InterpolationNode,
} from "../../../compiler";
import { getRootNode } from "./helper";

import {
    transformExpression
} from "./transformExpression"

export function transformText(node:TextNode | InterpolationNode) {

    let { type ,value } = node;

    if (type !== NodeTypes.INTERPOLATION && type !== NodeTypes.TEXT) return ;

    node.codegenNode = {
        children:transformExpression({
            node,
            root:getRootNode(node),
            exp:value,
            isReplaceExpression:true
        }) as string,
        type:node.type,
    }

  
}
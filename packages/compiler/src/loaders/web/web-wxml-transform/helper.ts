
  
  
  import {
    ElementNode,
    TemplateNode,
    RootNode,
    ElementCodegenNode,
    NodeTypes,
    ElementTypes,
    CodegenNode
  } from "../../../compiler"

  //@ts-ignore
  import _ from "lodash";
  


  // 获取节点元素
  export function getSblingNode(node:ElementNode,num:number) {

    let { parent } = node;
  
    let { children = [] } = parent!
  
    let index = _.findIndex(children,(item) => item === node)
  
    if (index !== -1) return children[index + num];
  
  }


  // 获取节点元素
  export function getSblingConditionNode(node:ElementNode,target:ElementNode) {

    if (!node) return ;
    if (node.type === NodeTypes.ELEMENT && node !== target ) {

        const { props = [] } = node;

        const hasCondition = props.some((prop) => {
          //@ts-ignore
          return prop.dirname &&  (prop.dirname === "elif" || prop.dirname === "if")
        })
        if (hasCondition) return node;
    }
    
    const prevNode = getSblingNode(node,-1);

    //@ts-ignore
    return getSblingConditionNode(prevNode ,target)

  
 
  
  }



export function getRootNode(node:TemplateNode | RootNode) {
    //@ts-ignore
    return node?.type === NodeTypes.ROOT ? node : getRootNode(node.parent)
}

export const expressionReg = /{{\s*([\s\S]*?)\s*}}/g;

export function getExpMatches(exp:string) {

    expressionReg.lastIndex = 0;

    let matches = exp.match(expressionReg)

    return matches
}



export function checkNodeHasForSameName(node:TemplateNode,name:string):boolean {

  
    if (!node) return false
    //@ts-ignore
    let { parent , forInfo } = node;

    if (forInfo) {
      let { itemName ,indexName } = forInfo
      if (itemName === name || indexName === name) return true
    }

    if (!parent) return false;

    return checkNodeHasForSameName(parent as ElementNode ,name)

}


export function isWxsModule(name:string,node:RootNode) {
    let { imports } = node;

    const wxsImports = _.filter(imports,(item) => item.type === ElementTypes.WXS);
    if (!wxsImports) return 

    const has = wxsImports.some((wxs) => {

        const {props = [] } = wxs;
        const match = _.find(props, { value :name})

        return match
    })

  
    return has
}


export function isMethods(name:string,node:RootNode) {
    let { methods } = node;
  
    return !!methods.includes(name)
}


// 是不是其他作用于的变量，wxs/for 都不不是组件的数据

export function isDataPath(path:any,node:TemplateNode,rootNode:RootNode) {

    let { parentPath,node:idenNode } = path;
    let parentNode = parentPath.node;
    let { data } = rootNode


    if (
        parentPath &&
        parentNode.object &&
        !parentNode.computed && 
        parentNode.object !== idenNode

      )
        return; // 对象的子属性

      if (
        parentPath.isObjectProperty() &&
        parentNode.key === idenNode
      )
        return; // 对象的key


    if (parentPath.isVariableDeclarator()) return ;

    let _name = idenNode.name

  

    if (checkNodeHasForSameName(node ,_name)) return;
    if (isWxsModule(_name,rootNode)) return ;
    if (isMethods(_name,rootNode)) return ;
    if (data.includes(_name))    return ;

    return true;

}



// 对事件进行单独处理
export function processEventsCodeGen(node:ElementNode , codegenNode:ElementCodegenNode) {

    const   { propsCodegenNode  =  {  }} = codegenNode as any
     const { props:propsCodegenList = []} = propsCodegenNode

     const eventPropsCodeGen = [];

     for (let i = 0 ; i < propsCodegenList.length;i++) {

      const item = propsCodegenList[i];
      const { subType } = item;
      if (subType === NodeTypes.EVENT) {
        eventPropsCodeGen.push(item);
        propsCodegenList.splice(i,1);
        i--;
      }

     }

     /**
      *  isCatch ,
            key : eventName.toLowerCase(),
            value :  exp
      */
     if (!eventPropsCodeGen.length) return ;

     let eventCogenGen = `\${[
        ${eventPropsCodeGen.map((item) => {
          const { iscatch , key ,value } = item;
            return `{
                name : "${key}",
                handler:processDomEventHandler.call(this,${value}),
                iscatch : ${iscatch}
            }`
        }).join(",")}
     
      ]}`

      codegenNode.propsCodegenNode.props.push({
        type:NodeTypes.DYNAMATICPROPS,
        key :".concise_events",
        value:eventCogenGen
      })

}


export function attachChildrenCodegen(node: ElementNode,codegenNode:CodegenNode) {

  if (!node) return;
  let { children = [] } = node;

  if (!children.length) return;

  //@ts-ignore
  codegenNode!.children = getChildrenCodegen(node)

  // // 如果只有一条text的话，直接给了，不用搞这么复杂
  // if (ElementNodeHasOnlyTextChild(node)) {
  //   //@ts-ignore
  //   codegenNode!.children = children[0].codegenNode?.children;
  // } else {
  //   //@ts-ignore
  //   codegenNode!.children = getChildrenCodegen(node)
  // }
}


export function getChildrenCodegen(ast:TemplateNode | RootNode) {
  return ast.children?.filter((child) => !child.isRemoved)
  .map((child) => child.codegenNode)
}



export function ElementNodeHasOnlyTextChild(node: TemplateNode) {

  if (!node) return false;
  let { children = [] } = node;

  if (!children.length) return false;

  return children.length === 1 && (
    children[0].type === NodeTypes.TEXT ||
    children[0].type === NodeTypes.INTERPOLATION)
}


export function findTemplateDefineNode(node:TemplateNode,root:RootNode) {


  if (!node) return root;
  const { parent = {} } = node;

  

  //@ts-ignore
  const { tag ,props = {} , type } = parent 

  if (type === NodeTypes.ROOT) return parent;

  if (tag && tag === "template" && _.find(props,{ key :"name"})) {
    return parent
  }

  return findTemplateDefineNode(parent as TemplateNode,root)

}

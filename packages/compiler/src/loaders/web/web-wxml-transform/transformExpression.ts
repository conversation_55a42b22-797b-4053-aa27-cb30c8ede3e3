
 import {
    isDataPath,
    getRootNode,
    getExpMatches,
    expressionReg
} from "./helper"

import {
    RootNode,
    Node,
    TemplateNode,
    stringType
} from "../../../compiler"

 import {
    readAst,
    generateCodeByAst,
    deleteBrackets,

 } from '../../../util'


 const babel = require('@babel/core');
 const traverse = require('@babel/traverse').default;
 const t = require('@babel/types');
 import { types } from '@babel/core'



 //isCompact 是否组合成模版表达式，主要用于属性用 "name {{xxx}} bajie" -> "name ${xxxx} bajie"
export type TransformParams = {
    exp:string,
    node:Node,
    root:RootNode,
    // isIndepenetParams?:boolean // 如果是表达式，是否独立参数出来
    // isAddQuoto ? :boolean,//  对于非表达式点字符串，是否增加引号 ,text其实不需要，字符串属性的直接可以加其实
    // prefix?:string

    isReplaceExpression ? :boolean// 是否替换{{}} 为 ${} 一般都要替换，click不需要，因为还要加this处理

}

// 

export function processExpression(str:string,node,root) {
    let ast = readAst(str);

    traverse(ast!, {

       
        //@ts-ignore
        Identifier:(path) => {
            
            if (!isDataPath(path,node as TemplateNode,root)) return 
            let name = path.node.name;
            if (hasDone(name)) return ;
            path.node.name = `_ctx.${name}`

        }
    })

    let  ret = generateCodeByAst(ast);

   
    // 链式操作.->?.  [][]->[]?.[]
    ret = ret.replace(/;$/, '').replace(/(?<=[\w\]])\.(?!\d)/g, '?.').replace(/(?<=[\]])\[/g, '?.[');
    return ret
}


function hasDone(str:string) {
    return str === "_ctx"
} 
export function transformExpression(params:TransformParams) :  string {

        let { exp , node,root,isReplaceExpression   } = params

        if (exp === undefined) return "true";

        if (!exp) return exp;

        if (/^\d+$/.test(exp)) return exp; // 纯数字

        let booleanReg = /^\s*{{\s*((?:true)|(?:false))\s*}}\s*$/
        let booleanMatch = exp.match(booleanReg)
        if (booleanMatch) return booleanMatch[1]

        let matches = getExpMatches(exp)
        let res = exp;
        let hasDynamatic = false

        if (matches) {
            hasDynamatic = true
            res = exp.replace(expressionReg,((match:string) => {
                let str = deleteBrackets(match);

                 str = processExpression(str,node,root);
                 if (isReplaceExpression) str = "${" + str + "}"
                 return str
            }))
        }
        
      

        return res;


}
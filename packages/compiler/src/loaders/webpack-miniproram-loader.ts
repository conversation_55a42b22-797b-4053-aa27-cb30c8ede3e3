
import { NormalModule } from "webpack";
import {
    getFileExtName,
} from "../util"
import { parseReuqest } from "../webpack-plugins/helper";

import {
    LoaderContext
} from "webpack"

import {
    processScriptFile,
    processWxmlFile,
    processWxsFile,
    processJsonFile,
    processStyleFile
} from "./mini"




export default async function miniprogram_loader(this:LoaderContext<any>,source:string) {

    let { _module, } = this;

    const callback = this.async();

    const { userRequest  } = _module as NormalModule;

    const parsed  = parseReuqest(userRequest);

    if (!parsed) return  callback(null,source);

    const { type } = parsed;


    switch(type) {


        case "wxs":
            source = await processWxsFile.call(this,source)
            break;

        case "wxml":
            source = await processWxmlFile.call(this,source);
            break;

        case "json":
            source = await processJsonFile.call(this,source);
            break;

        case "style" :
            source = await processStyleFile.call(this,source);
            break;

        case "script":
            source =await  processScriptFile.call(this,source);
            break;
    }


     callback(null,source)

}



import { NormalModule,LoaderContext } from "webpack";

import { parseReuqest } from "../webpack-plugins/helper";

import {
    processWxmlFile,
    processScriptFile,
    processWxsFile,
    processJsonFile,
    processStyleFile
} from "./web/index"






export default function web_loader(source: string) {

    let { _module, } = this;


    const { userRequest } = _module as NormalModule;

    const parsed = parseReuqest(userRequest);


    if (!parsed) return  source

    const { type } = parsed;

    
    switch (type) {
        case "wxml":
            source = processWxmlFile.call(this, source);
            break;

        case "script":
            source = processScriptFile.call(this, source);
            break;

        case "wxs":
            source = processWxsFile.call(this, source);
            break;

        case "json":
            source = processJsonFile.call(this, source);
            break;

        case "style":
            source = processStyleFile.call(this, source);
            break;


    }

    return source

}

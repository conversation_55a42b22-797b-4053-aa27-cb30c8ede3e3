

export * from "./types"


import { XData , SqbComponentOptions } from "./types";




export function isWechatPlatform():boolean {
  //@ts-ignore
    return typeof wx !== 'undefined' && !!wx.getSystemInfo
}


export function isWebPlatform():boolean {

  return typeof window !== 'undefined' && typeof window.document !== 'undefined';
}

export function getTag(value:any) {
    return Object.prototype.toString.call(value);
  }


  export function isUndefined(value:any) :boolean {

    return value === undefined
  }


  export const WePropertyConstructor = [
    String,
    Number,
    Boolean,
    Array,
    Object,
  ]


  export const aliComponentMap = {
    data: 'data',
    properties: 'props',
    relations:"relations",
    behaviors: 'mixins',
    methods: 'methods',
    attached: 'onInit',
    ready: 'didMount',
    detached: 'didUnmount',
    error: 'onError',
    options: 'options',
    lifetimes :"lifetimes",
    observers:"observers"
  };



  export function mapKeys(source:XData, target:XData, map:XData) {
    Object.keys(map).forEach((key) => {
      if (source[key]) {
        target[map[key]] = source[key];
      }
    });
  }

  export const CONCISE_PROXY_DATA = "_CONCISE_PROXY_DATA_"



  export type PropertyHandler = {
    nullProcess?:Function,
    constructorProcess?:Function,
    objectProcess?:Function
  }
  export function mapWechatProperties(properties:XData,handler:PropertyHandler) {

    for (const key in properties) {
      const item: any = properties[key];
      if (item === null) {
         if (handler.nullProcess) handler.nullProcess(key,item)
          //@ts-ignore
      } else if (WePropertyConstructor.includes(item)) {
        if (handler.constructorProcess) handler.constructorProcess(key,item)
      }
      else {
        if (handler.objectProcess) handler.objectProcess(key,item)
      }

  }


  }



export function ensureComponentOptions(options:SqbComponentOptions) {
  if (!options.data) options.data = {}
  if (!options.properties) options.properties = {}
  if (!options.methods) options.methods = {}
  if (!options.behaviors) options.behaviors = []
  if (!options.observers) options.observers = {}
  if (!options.options) options.options = {};
  options.options = Object.assign({ "styleIsolation": "isolated" },options.options)

}



export type XData = Record<any, any>



export type SqbComponentLiftimes = {


    lifetimes?: Partial<{
        created(): void
        attached(): void
        ready(): void
        moved(): void
        detached(): void
        error(err: Error): void
    }>
    created?: (params: any) => void
    attached?: (params: any) => void

    ready?: (params: any) => void

    moved?: (params: any) => void

    detached?: (params: any) => void

    error?: (params: any) => void
}

export type SqbComponentOptions = {
    data?: WechatMiniprogram.Component.DataOption,
    properties?: WechatMiniprogram.Component.PropertyOption,
    externalClasses?:Array<string>,
    methods?: WechatMiniprogram.Component.MethodOption,
    td?: {
        [k in string]?:{
            type:"throttle" | "debounce",
            delay?:number
        }
    },
    relations?: WechatMiniprogram.Component.RelationOption,
    observers?: Record<string, (...args: any[]) => any>,
    behaviors?: any[],

    options?: XData,

    pageLifetimes?: {
        show?: (params: any) => void
        hide?: (params: any) => void
        resize?: (params: any) => void
    },

    isConciseRoot?:boolean

    concise_behavior_id ? :string
    concise_isPage?:boolean,
    concise_isComponent ? :boolean
} & SqbComponentLiftimes


export type SqbPageOptions = {


    data?:XData
    options?:XData
    behaviors?:XData
    onLoad?:Function
    onShow?:Function
    onReady?:Function
    onHide?:Function
    onUnload?:Function
    onRouteDone?:Function
    onPullDownRefresh?:Function
    onReachBottom?:Function
    onShareAppMessage?:Function
    concise_isPage?:boolean,
    concise_isComponent ? :boolean
}

export enum PLATFORM {
    WECHAT = 0,
    ALIPAY = 1
}


export type IContext = {
    handler: {
        getComponentOptions: (options: SqbComponentOptions) => XData
        isRoot:() => boolean
    },
    options: SqbComponentOptions
}



export type ICreateComponentParams = {

    render: Function,
    styles: string,
    tag:string,
    request:string, // 文件路径
    componentPath:string //组件表达方式，去掉后缀
}
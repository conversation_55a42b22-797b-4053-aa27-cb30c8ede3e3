const {
    pack,
} = require("@wosai/smart-mp-concise-compiler")

process.on("message", (params) => {
    const { configPath ,index , watch,production}  = params

    let configLists = require(configPath);

    const config = configLists[index];

    pack({ ...config,watch,production})
    .then(() => {
        process.exit()
    },(err) => {
        console.log(err)
        process.exit()
    })
});
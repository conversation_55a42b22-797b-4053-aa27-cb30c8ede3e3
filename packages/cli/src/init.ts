


import fs from "fs-extra";
import path from "path"
const { execSync } = require('child_process');
import
inquirer
  from "inquirer"


import {
  Command
} from "commander"








const copyTemplate = async (outputDir: string) => {

  if (fs.existsSync(outputDir)) {
    console.log("目录已存在 : ", outputDir)
    process.exit();
  }


  const src_template = path.resolve(__dirname,"../template")
  console.log(`正在从复制模版.....`)
  fs.copySync(src_template,outputDir);




    console.log(`模版复制完成!`)
  

};



const modifyPackageJson = async (dir: string, params: any) => {
  const packagePath = `${dir}/package.json`
  // 读取 package.json
  const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));

  // 修改 name 和 author 字段
  Object.keys(params).forEach((key) => {
    packageJson[key] = params[key]
  })


  // 写回修改后的 package.json
  fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2), 'utf8');

};




const installDependencies = (dir) => {
  console.log('正在安装依赖...');
  execSync('npm install', { cwd: dir, stdio: 'inherit' });
};



export function createInitTemplateCommand(commander: Command) {
  commander
    .command('init [projectName]')
    .description('Initialize a new project')
    .action(async (projectName) => {
      if (!projectName) {
        //@ts-ignore
        const answers = await inquirer.prompt([
          {
            type: 'input',
            name: 'projectName',
            message: '请输入工程名称:',
            default: 'my-project'
          }
        ]);
        projectName = answers.projectName;
      }

       //@ts-ignore
      const answers = await inquirer.prompt([
        {
          type: 'input',
          name: 'author',
          message: '作者名字:',
          default: 'weixuhong'
        },
      ]);

      const author = answers.author;
      const targetDir = `${process.cwd()}/${projectName}`


      await copyTemplate(targetDir);
      modifyPackageJson(targetDir, { name: projectName, author })
      installDependencies(targetDir)


      console.log(`Project ${projectName} initialized successfully!`);
    });


}
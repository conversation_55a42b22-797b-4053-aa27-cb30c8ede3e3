
import {
    Command
} from "commander"

import {
    ICompilerOptions,
} from "@wosai/smart-mp-concise-compiler"


import { fork } from "child_process";
import path from "path";

import _ from "lodash"


export function defineConfig(params: Partial<ICompilerOptions>[]) {

    return params;

}


export async function _compile(options) {
    const { watch, production, config , name } = options

    const context = process.cwd();
    const jsConfig = path.join(context, config)
    let configLists = require(jsConfig);

    if (name) {
        configLists = _.filter(configLists, (item) => {
            return item.name === name
        })
    }

    let children = [];
    configLists.forEach((_, index) => {
        const child = fork(path.resolve(__dirname, "../worker/run.js"), [], {
            env: { ...process.env } // 设置子进程的环境变量
        });

        child.send({
            configPath: jsConfig,
            index,
            production: !!production,
            watch: !!watch
        }); // 向子进程发送 Webpack 配置

        children.push(child)
    })


    // 监听父进程的退出信号（比如 Ctrl+C）
    const cleanup = () => {

        // 遍历所有子进程并杀死它们
        children.forEach(child => {
            child.kill('SIGINT');  // 向子进程发送 SIGINT 信号，表示终止
        });
    };

    // 捕获 Ctrl+C (SIGINT)
    process.on('SIGINT', cleanup);
    // 捕获退出信号 (如 kill 命令)
    process.on('exit', cleanup);

    // 你还可以添加其他的进程信号处理器，如 SIGTERM
    process.on('SIGTERM', cleanup);



    //    // 创建线程执行 pack(config)
    //    const runPackInThread = ({
    //     index,
    //    }
    //    ) => {
    //       return new Promise((resolve, reject) => {
    //           const worker = new Worker(
    //               __filename, // 当前文件名
    //               { workerData: { options: {
    //                 index,
    //                 context,
    //                 watch
    //               }} } // 传递 config 和 watch 参数
    //           );

    //           worker.on('message', resolve);
    //           worker.on('error', reject);
    //           worker.on('exit', (code) => {
    //               if (code !== 0) {
    //                   reject(new Error(`Worker stopped with exit code ${code}`));
    //               }
    //           });
    //       });
    //   };

    //   // 并行执行所有 config
    //   Promise.all(configLists.map((config,index) =>  runPackInThread({
    //     index
    //   })))
    //       .then(results => {
    //           console.log('All packs completed:', results);
    //       })
    //       .catch(err => {
    //           console.error('Error:', err);
    //       });



}

export function createCompileCommand(commander: Command) {


    commander
        .command('compile')
        .description('compile concise project')
        .option('-w, --watch', '是否监听模式', false)
        .option('-c, --config <path>', '配置文件', './concise.config.js')
        .option('--production', '是否是正式环境', false)
        .option('-n, --name <name>', '指定编译的包名或项目名')
        .action(async (options) => {
            await _compile(options)
        })
}

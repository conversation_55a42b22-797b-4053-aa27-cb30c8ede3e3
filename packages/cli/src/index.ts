import { program } from 'commander';



import fse from "fs-extra"

import {
  createInitTemplateCommand
} from "./init"

import {
  createCompileCommand,
  defineConfig,_compile
} from "./compile"

export {
  defineConfig,
  _compile
}

import {
    resolve
} from "./util"

const pkg = JSON.parse(fse.readFileSync(resolve("../package.json")));

export function run () {

  program
  .version(pkg.version)
  .description('A CLI For Concise');


createInitTemplateCommand(program);
createCompileCommand(program);


// 解析命令行参数
program.parse(process.argv);
}






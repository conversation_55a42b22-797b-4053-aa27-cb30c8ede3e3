{
  "compilerOptions": {

    "baseUrl": ".",
    "outDir": "dist",
    "sourceMap": false,
    "target": "es2015",
    "useDefineForClassFields": false,
    "module": "esnext",
    "moduleResolution": "node",
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "allowJs": false,
    "strict": false,
    "experimentalDecorators": true,
    "resolveJsonModule": true,

    "removeComments": false,
    "lib": ["esnext", "dom"],
    "strictNullChecks":false,
    "strictPropertyInitialization": false,
      "declaration": true,
    "emitDeclarationOnly": false,
    "declarationMap": false,

    "types": ["jest", "node","miniprogram-api-typings"],
    "paths": {
        "@wosai/smart-mp-concise-*":["packages/*/src/index.ts"],
    }
  },
  "include": [
    "packages/*/src",
    "packages/global.d.ts",
  ],
  "exclude": [
    "packages/*/__tests__"
  ]
}

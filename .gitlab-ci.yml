image: jfrog.wosai-inc.com/docker-virtual-prod/middleware/node-builder:20

stages:
  - build
  - release

cache:
  paths:
    - node_modules
    - .pnpm-store
  key: ${CI_COMMIT_REF_SLUG}
  policy: pull-push
  when: 'always'

variables:
  NODE_OPTIONS: --max-old-space-size=32768
  NPM_CONFIG_REGISTRY: https://registry-npm.wosai-inc.com
  NODE_VERSION: 20

before_script:
  - node --version
  - npm config set registry https://registry-npm.wosai-inc.com
  - npm config set strict-ssl false
  - echo "Node.js 详细信息："
  - node -p "process.versions"
  - echo "当前环境变量："
  - env | grep NODE
  # 检查包管理器
  - which pnpm || npm install -g pnpm
  - pnpm config set registry https://registry-npm.wosai-inc.com
  - pnpm config set strict-ssl false
  # 安装依赖 (使用pnpm支持workspace)
  - pnpm install
  # Git 配置
  - |
    git config --global user.name "${GITLAB_USER_NAME}"
    git config --global user.email "${GITLAB_USER_EMAIL}"
    git config --global core.warnambiguousrefs false
    git remote set-url origin "https://gitlab-ci-token:$GIT_ACCESS_TOKEN@$CI_SERVER_HOST/$CI_PROJECT_PATH.git"

# 构建阶段
build:
  stage: build
  tags:
    - jfrog-dev
  script:
    - echo "开始构建 smart-mp-concise 项目..."
    - pnpm run build
    - echo "构建完成！"
  artifacts:
    paths:
      - packages/*/dist/
      - packages/*/lib/
    expire_in: 1 hour
  only:
    - merge_requests
    - master
    - develop
    - /^(feature|release|hotfix)\/.*$/

# 测试版本发布 (feature/release 分支)
release-beta:
  stage: release
  tags:
    - jfrog-dev
  script:
    - |
      echo "发布测试版本..."
      
      # 1. 更新版本为 beta
      npx lerna version prepatch \
        --force-publish "*" \
        --yes \
        --no-git-tag-version \
        --conventional-commits \
        --preid beta \
        --loglevel verbose
      
      # 2. 提交更改
      git add .
      git commit -m "chore: bump beta version [skip ci]"
      
      # 3. 发布包
      npx lerna publish from-package \
        --force-publish \
        --no-git-reset \
        --yes \
        --dist-tag beta \
        --loglevel verbose \
        --registry https://registry-npm.wosai-inc.com/
    
    - NODE_ENV=beta node ./scripts/notify.js
  only:
    - /^(feature|release).*$/
  dependencies:
    - build

# 正式版本发布 (tags)
release-production:
  stage: release
  tags:
    - jfrog-dev
  script:
    - |
      echo "发布正式版本 ${CI_COMMIT_TAG}..."
      
      # 1. 设置版本为 tag 版本
      npx lerna version "${CI_COMMIT_TAG}" \
        --force-publish \
        --yes \
        --no-git-tag-version \
        --conventional-commits \
        --loglevel verbose
      
      # 2. 提交更改
      git add .
      git commit -m "chore: release version ${CI_COMMIT_TAG} [skip ci]"
      
      # 3. 发布包
      npx lerna publish from-package \
        --force-publish \
        --no-git-reset \
        --yes \
        --loglevel verbose \
        --registry https://registry-npm.wosai-inc.com/
    
    - NODE_ENV=production node ./scripts/notify.js
  only:
    - tags
  dependencies:
    - build

# 手动触发发布
release-manual:
  stage: release
  tags:
    - jfrog-dev
  script:
    - |
      echo "手动触发发布..."
      
      # 1. 更新补丁版本
      npx lerna version patch \
        --force-publish "*" \
        --yes \
        --no-git-tag-version \
        --conventional-commits \
        --loglevel verbose
      
      # 2. 提交更改
      git add .
      git commit -m "chore: manual release [skip ci]"
      
      # 3. 发布包
      npx lerna publish from-package \
        --force-publish \
        --no-git-reset \
        --yes \
        --loglevel verbose \
        --registry https://registry-npm.wosai-inc.com/
    
    - NODE_ENV=manual node ./scripts/notify.js
  when: manual
  only:
    - master
  dependencies:
    - build
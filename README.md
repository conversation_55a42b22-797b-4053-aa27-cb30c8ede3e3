# Smart MP Concise



#  todo:

### web
1. 属性定义observer
2. router
3. sqb api
4. native component
5. 父子组件事件
6. 生命周期执行 onshow/onhide
6. app/page/component的onload入参
7. 直接进页面功能 dev-server配置
8. 静态资源的处理



### alipay
1. 编译的时候替换Behavior



### 注意事项
1. 属性驼峰严格要求
2. wxs中关于数组和对象的判断差异
3. :empty伪类 lit element不支持，他给元素内部会加上自己代码，导致innerHtml始终不为空
4. template调用参数只能传data
5. iconfont 在 shadow DOM 内 浏览器不下载，特殊处理，手动放到html的<style>标签，时其下载
6. externalclasses 的优先级问题 （注意：在同一个节点上使用普通样式类和外部样式类时，两个类的优先级是未定义的，因此最好避免这种情况。）
7. wxml里语法的保护c = a.b.c 即使a不存在 微信也不回报错，这部分没处理，需要业务者自己判断 c = a&&a.b&&a.c
8. ememu-util 的storage 没序列化
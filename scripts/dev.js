const fs = require('fs-extra')
const path = require('path')
const chalk = require('chalk')
const execa = require('execa')
const { gzipSync } = require('zlib')
const { compress } = require('brotli')

const { build } = require("./utils")

const args = require('minimist')(process.argv.slice(2))
const targets = args._

if (!targets.length) {
    console.error(`dev must have target`)
    process.exit(1)
}





run()

async function run() {
    let target = targets[0]

    build(target , { w : true})

}

  

  
  
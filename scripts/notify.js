const fs = require('fs')
const path = require('path')
const request = require('request')

const env = process.env.NODE_ENV || 'beta'

const lernaJSON = fs.readFileSync(path.resolve('./lerna.json'), 'utf8')
const packageJSON = fs.readFileSync(path.resolve('./package.json'), 'utf8')

try {
  const {
    COMMIT_MESSAGE,
    CI_COMMIT_AUTHOR,
    CI_COMMIT_TITLE,
    CI_COMMIT_MESSAGE,
    CI_COMMIT_DESCRIPTION,
    CI_JOB_URL,
    CI_MERGE_REQUEST_DIFF_ID,
    CI_COMMIT_TAG,
    CI_COMMIT_BRANCH
  } = process.env

  let version
  if (env === 'production') {
    version = CI_COMMIT_TAG
  } else {
    ;({ version } = JSON.parse(lernaJSON))
  }

  const { name: projectName } = JSON.parse(packageJSON)

  // 根据环境选择不同的webhook和样式
  const envConfig = {
    production: {
      url: 'https://open.feishu.cn/open-apis/bot/v2/hook/9e723bd2-6c3c-4cbd-af5d-1118b47d62a8',
      template: 'green',
      title: '📦 smart-mp-concise 正式版本发布通知',
      emoji: '🎉'
    },
    beta: {
      url: 'https://open.feishu.cn/open-apis/bot/v2/hook/9e723bd2-6c3c-4cbd-af5d-1118b47d62a8',
      template: 'blue',
      title: '🧪 smart-mp-concise 测试版本发布通知',
      emoji: '🚀'
    },
    manual: {
      url: 'https://open.feishu.cn/open-apis/bot/v2/hook/9e723bd2-6c3c-4cbd-af5d-1118b47d62a8',
      template: 'orange',
      title: '🔨 smart-mp-concise 手动发布通知',
      emoji: '⚡'
    }
  }

  const config = envConfig[env] || envConfig.beta
  
  const content = `${config.emoji} smart-mp-concise 小程序框架发布成功

📦 版本信息
**项目名称：** ${projectName || '@wosai/smart-mp-concise'}
**版本号：** ${version}
**分支：** ${CI_COMMIT_BRANCH}
**提交者：** ${CI_COMMIT_AUTHOR || '未知'}
**发布环境：** ${env}

📝 提交信息
${CI_COMMIT_MESSAGE || COMMIT_MESSAGE || '无提交信息'}
${CI_COMMIT_DESCRIPTION ? `📋 详细描述: ${CI_COMMIT_DESCRIPTION}` : ''}

🔍 NPM 包链接
⚠️ 请检查以下 NPM 包是否成功发布：

- [📦 @wosai/smart-mp-concise-cli](https://web-npm.wosai-inc.com/package/@wosai/smart-mp-concise-cli) ✅ 命令行工具
- [📦 @wosai/smart-mp-concise-compiler](https://web-npm.wosai-inc.com/package/@wosai/smart-mp-concise-compiler) ✅ 编译器
- [📦 @wosai/smart-mp-concise-runtime-base](https://web-npm.wosai-inc.com/package/@wosai/smart-mp-concise-runtime-base) ✅ 基础运行时
- [📦 @wosai/smart-mp-concise-runtime-mini](https://web-npm.wosai-inc.com/package/@wosai/smart-mp-concise-runtime-mini) ✅ 小程序运行时
- [📦 @wosai/smart-mp-concise-runtime-web](https://web-npm.wosai-inc.com/package/@wosai/smart-mp-concise-runtime-web) ✅ Web组件运行时
- [📦 @wosai/smart-mp-concise-shared](https://web-npm.wosai-inc.com/package/@wosai/smart-mp-concise-shared) ✅ 共享工具
- [📦 @wosai/smart-mp-concise-template](https://web-npm.wosai-inc.com/package/@wosai/smart-mp-concise-template) ✅ 项目模板

${CI_JOB_URL ? `🔗 [查看构建详情](${CI_JOB_URL})` : ''}

📚 文档链接
- [🏠 项目首页](https://gitlab.wosai-inc.com/frontend/smart-mp/smart-mp-concise)
- [📖 使用文档](https://gitlab.wosai-inc.com/frontend/smart-mp/smart-mp-concise/-/blob/master/README.md)
- [🔧 更新日志](https://gitlab.wosai-inc.com/frontend/smart-mp/smart-mp-concise/-/blob/master/CHANGELOG.md)`

  let jsonData = {
    msg_type: 'interactive',
    card: {
      config: {
        wide_screen_mode: true
      },
      elements: [
        {
          tag: 'markdown',
          content
        }
      ],
      header: {
        template: config.template,
        title: {
          content: config.title,
          tag: 'plain_text'
        }
      }
    }
  }

  const options = {
    url: config.url,
    headers: {
      'Content-Type': 'application/json'
    },
    json: jsonData
  }

  request.post(options, (error, response, body) => {
    if (error) {
      console.error('飞书通知发送失败:', error)
    } else {
      console.log('飞书通知发送成功!')
      console.log('通知内容:', content)
    }
  })
} catch (e) {
  console.error('飞书通知脚本执行失败:', e)
}
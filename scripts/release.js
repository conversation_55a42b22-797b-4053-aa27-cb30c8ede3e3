const args = require("minimist")(process.argv.slice(2));
const fs = require("fs");
const path = require("path");
const chalk = require("chalk");
const semver = require("semver");
const currentVersion = require("../package.json").version;
const { prompt } = require("enquirer");
const execa = require("execa");

// const preId =
//   args.preid ||
//   (semver.prerelease(currentVersion) && semver.prerelease(currentVersion)[0]);

const isDryRun = args.dry;
const skipTests = args.skipTests;
const skipBuild = args.skipBuild;
const  { targets : packages , updateVerionPackages }  = require("./utils");

const skippedPackages = [];

const preId =
  args.preid ||
  (semver.prerelease(currentVersion) && semver.prerelease(currentVersion)[0])

const versionIncrements = [
  "patch",
  "minor",
  "major",
  ...(preId ? ["prepatch", "preminor", "premajor", "prerelease"] : []),
];

const inc = (i, preId) => semver.inc(currentVersion, i, preId);

const bin = (name) => path.resolve(__dirname, "../node_modules/.bin/" + name);
const run = async (bin, args, opts = {}) => await execa(bin, args, { stdio: "inherit", ...opts });
const dryRun = (bin, args, opts = {}) => console.log(chalk.blue(`[dryrun] ${bin} ${args.join(" ")}`), opts);
const runIfNotDry = isDryRun ? dryRun : run;
const getPkgRoot = (pkg) => path.resolve(__dirname, "../packages/" + pkg);
const step = (msg) => console.log(chalk.cyan(msg));


async function main() {
  let commitMessage = args._[0];

  console.log(`commitMessage is ${commitMessage}`,args)

    // no explicit version, offer suggestions
    const { release } = await prompt({
      type: 'select',
      name: 'release',
      message: 'Select release type',
      choices: versionIncrements.map(i => `${i} (${inc(i)})`).concat(['custom'])
    })

    if (release === 'custom') {
      targetVersion = (
        await prompt({
          type: 'input',
          name: 'version',
          message: 'Input custom version',
          initial: currentVersion
        })
      ).version
    } else {
      targetVersion = release.match(/\((.*)\)/)[1]
    }
  

  if (!semver.valid(targetVersion)) {
    throw new Error(`invalid target version: ${targetVersion}`)
  }

  const { yes } = await prompt({
    type: 'confirm',
    name: 'yes',
    message: `Releasing v${targetVersion}. Confirm?`
  })

  if (!yes) {
    return
  }

  if (!semver.valid(targetVersion)) {
    throw new Error(`invalid target version: ${targetVersion}`);
  }

  step(`\nTargetVersion is ${targetVersion}`);


  if (!commitMessage) {
    commitMessage = `release: v${targetVersion}`;
  }
 
  // step("\nRunning tests...");
  // if (!skipTests && !isDryRun) {
  //   await run(bin("jest"), ["--clearCache"]);
  //   await run("pnpm", ["test", "--bail"]);
  // } else {
  //   console.log(`(skipped)`);
  // }

  // update all package versions and inter-dependencies
  step("\nUpdating cross dependencies...");
  updateVersions(targetVersion);

  // build all packages with types
  step("\nBuilding all packages...");
  if (!skipBuild && !isDryRun) {
    await run("pnpm", ["run", "build", "--release"]);
  } else {
    console.log(`(skipped)`);
  }

  
  // publish packages
  step("\nPublishing packages...");
  for (const pkg of packages) {
    await publishPackage(pkg, targetVersion, runIfNotDry);
  }

  updateVerionPackages.forEach((p) => updatePackage(getPkgRoot(p), "workspace:*"));

  const { stdout } = await run("git", ["diff"], { stdio: "pipe" });
  if (stdout) {
    step("\nCommitting changes...");
    await runIfNotDry("git", ["add", "-A"]);
    await runIfNotDry("git", ["commit", "-m", `${commitMessage}`]);
  } else {
    console.log("No changes to commit.");
  }


  

  // push to Git
  step("\nPushing to GitLab...");
  await runIfNotDry("git", ["tag", `v${targetVersion}`]);
  await runIfNotDry("git", ["push", "origin", `refs/tags/v${targetVersion}`]);
  await runIfNotDry("git", ["push"]);

  if (isDryRun) {
    console.log(`\nDry run finished - run git diff to see package changes.`);
  }

  if (skippedPackages.length) {
    console.log(chalk.yellow(`The following packages are skipped and NOT published:\n- ${skippedPackages.join("\n- ")}`));
  }
  console.log();
}

function updateVersions(version) {
  // 1. update root package.json
  const pkgRoot = path.resolve(__dirname, "..");
  const pkgPath = path.resolve(pkgRoot, "package.json");
  const pkg = JSON.parse(fs.readFileSync(pkgPath, "utf-8"));
  pkg.version = version;
  fs.writeFileSync(pkgPath, JSON.stringify(pkg, null, 2) + "\n");


  // 2. update all packages
  updateVerionPackages.forEach((p) => updatePackage(getPkgRoot(p), version));
}

function updatePackage(pkgRoot, version) {
  const pkgPath = path.resolve(pkgRoot, "package.json");
  const pkg = JSON.parse(fs.readFileSync(pkgPath, "utf-8"));
  if (version !== "workspace:*") {
    pkg.version = version;
  }
  updateDeps(pkg, "dependencies", version);
  updateDeps(pkg, "peerDependencies", version);
  updateDeps(pkg, "devDependencies", version);
  fs.writeFileSync(pkgPath, JSON.stringify(pkg, null, 2) + "\n");
}

function updateDeps(pkg, depType, version) {
  const deps = pkg[depType];
  if (!deps) return;
  Object.keys(deps).forEach((dep) => {
    if (dep.startsWith("@wosai/smart-mp-concise") && packages.includes(dep.replace(/^@wosai\/smart-mp-concise-/, ""))) {
      console.log(chalk.yellow(`${pkg.name} -> ${depType} -> ${dep}@${version}`));
      deps[dep] = version
    }
  });
}

async function publishPackage(pkgName, version, runIfNotDry) {
  if (skippedPackages.includes(pkgName)) {
    return;
  }
  const pkgRoot = getPkgRoot(pkgName);
  const pkgPath = path.resolve(pkgRoot, "package.json");
  const pkg = JSON.parse(fs.readFileSync(pkgPath, "utf-8"));
  if (pkg.private) {
    return;
  }

  step(`Publishing ${pkg.name}...`);
  try {
    await runIfNotDry(
      // note: use of yarn is intentional here as we rely on its publishing
      // behavior.
      "npm",
      ["publish", "--registry https://registry-npm.wosai-inc.com/"],
      {
        cwd: pkgRoot,
        stdio: "pipe"
      }
    );
    console.log(chalk.green(`Successfully published ${pkg.name}@${version}`));
  } catch (e) {
    if (e.stderr.match(/previously published/)) {
      console.log(chalk.red(`Skipping already published: ${pkg.name}`));
    } else {
      throw e;
    }
  }
}

main()
.catch((err) => {
  updateVersions(currentVersion);
  console.error(err);
});

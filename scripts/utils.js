const fs = require("fs");
const chalk = require("chalk");

const path = require("path");
const execa = require('execa')

let ignorePackages = ["template"];


const targets = (exports.targets = fs.readdirSync("packages").filter((f) => {
  if (!fs.statSync(`packages/${f}`).isDirectory()) {
    return false;
  }

  if (ignorePackages.includes(f)) return false;
  return true;
}));


const updateVerionPackages = (exports.updateVerionPackages = fs.readdirSync("packages").filter((f) => {
  if (!fs.statSync(`packages/${f}`).isDirectory()) {
    return false;
  }

  return true;
}));

exports.fuzzyMatchTarget = (partialTargets, includeAllMatching) => {
  const matched = [];
  partialTargets.forEach((partialTarget) => {
    for (const target of targets) {
      if (target.match(partialTarget)) {
        matched.push(target);
        if (!includeAllMatching) {
          break;
        }
      }
    }
  });
  if (matched.length) {
    return matched;
  } else {
    console.log();
    console.error(`  ${chalk.bgRed.white(" ERROR ")} ${chalk.red(`Target ${chalk.underline(partialTargets)} not found!`)}`);
    console.log();

    process.exit(1);
  }
};



exports.build =  async function (target, options = {}) {
  const { w } = options; 
  const pkgRoot = path.resolve(__dirname, `../packages/${target}`)

  const pkg = JSON.parse(fs.readFileSync(`${pkgRoot}/package.json`,"utf-8"));


  try {

    if (pkg.selfBuild) {
      await execa(
        `npm run ${w ? "dev":"build"}`,
       
        {
          cwd: pkgRoot,
          stdio: 'inherit',
          shell: true // 允许执行 shell 命令  
        }
      )
    } else {

      let args = [
        "-c",
        '--environment',
        `TARGET:${target}`,
      ]
      if (w) {
        args.push("-w")
      }
      await execa(
        './node_modules/rollup/dist/bin/rollup',
        args,
        {
          stdio: 'inherit'
        }
      )

    }
  } catch (err) {
    console.log(err)
    process.exit()
  }

}
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Iconfont Example</title>
  
  <style>
    /* 定义 iconfont 字体 */
    @font-face {
      font-family: 'iconfont';  /* Project id 2336711 */
      src: url('https://at.alicdn.com/t/c/font_2336711_gd4nhny8p0e.woff2?t=1694574691854') format('woff2'),
           url('https://at.alicdn.com/t/c/font_2336711_gd4nhny8p0e.woff?t=1694574691854') format('woff'),
           url('https://at.alicdn.com/t/c/font_2336711_gd4nhny8p0e.ttf?t=1694574691854') format('truetype');
    }



   

.smart-icon-prefix {
  font-family: 'iconfont' !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: flex;
  justify-content: center;
  align-items: center;
}

.smart-icon-backspace:before {
  content: '\e6a7';
}

.smart-icon-red-packet:before {
  content: '\e6a6';
}

  </style>
</head>
<body>

</body>
</html>

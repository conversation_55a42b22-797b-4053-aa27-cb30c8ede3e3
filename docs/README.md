# Smart MP Concise 项目架构分析

## 项目概述

Smart MP Concise 是一个跨平台小程序开发框架，支持编译到微信小程序、支付宝小程序和 Web 组件。

## 整体架构

```
smart-mp-concise/
├── packages/
│   ├── cli/                    # 命令行工具
│   ├── compiler/               # 核心编译器
│   ├── runtime-base/           # 基础运行时
│   ├── runtime-mini/           # 小程序运行时
│   ├── runtime-web/            # Web运行时
│   ├── shared/                 # 共享工具库
│   └── template/               # 项目模板
├── scripts/                    # 构建脚本
└── docs/                       # 项目文档
```

## 核心特性

- 🔄 跨平台编译：一套代码，多端运行
- 📦 Monorepo 架构：模块化管理
- 🛠️ TypeScript 支持：类型安全
- ⚡ 热更新：开发体验优化
- 🎯 组件化：支持组件库和小程序开发

## 技术栈

- **构建工具**: Webpack, Rollup
- **包管理**: pnpm workspace
- **语言**: TypeScript
- **Web运行时**: Lit Element
- **CI/CD**: GitLab CI

## 模块分析文档

- [CLI 工具分析](./cli-analysis.md)
- [编译器分析](./compiler-analysis.md)
- [运行时分析](./runtime-analysis.md)
- [共享工具分析](./shared-analysis.md)
- [项目模板分析](./template-analysis.md)
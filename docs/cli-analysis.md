# CLI 工具分析 (@wosai/smart-mp-concise-cli)

## 模块职责

CLI 工具是框架的入口点，提供项目初始化、编译构建等核心功能。

## 核心功能

### 1. 命令定义
- `concise compile`: 编译项目
- `concise init`: 初始化项目模板

### 2. 配置管理
- 支持 `concise.config.js` 配置文件
- 提供 `defineConfig` 函数用于类型提示

### 3. 多进程编译
- 使用 worker 进程处理编译任务
- 支持多配置并行编译

## 代码结构

```
cli/
├── src/
│   ├── index.ts          # 主入口，导出核心API
│   ├── compile.ts        # 编译命令实现
│   ├── init.ts          # 初始化命令实现
│   └── util.ts          # 工具函数
├── bin/
│   └── index.js         # CLI 可执行文件
├── worker/
│   └── run.js           # Worker 进程脚本
└── template/            # 项目模板文件
```

## API 接口

### defineConfig
```typescript
export function defineConfig(config: ICompilerOptions | ICompilerOptions[]): any
```

### _compile
```typescript
export function _compile(options: ICompilerOptions): Promise<void>
```

## 依赖关系

- **依赖**: `@wosai/smart-mp-concise-compiler`
- **工具**: commander, inquirer, fs-extra

## 重构建议

### 优先级：中等

1. **命令扩展性**
   - 抽象命令注册机制
   - 支持插件化命令扩展

2. **配置验证**
   - 添加配置文件 schema 验证
   - 提供更好的错误提示

3. **多进程优化**
   - 改进 worker 进程通信
   - 添加进程池管理

### 重构路径

1. 抽离命令基类，统一命令接口
2. 实现配置验证中间件
3. 优化多进程架构，提升编译性能
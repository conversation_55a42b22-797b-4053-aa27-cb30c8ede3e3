# 编译器分析 (@wosai/smart-mp-concise-compiler)

## 模块职责

编译器是框架的核心，负责将源代码转换为目标平台代码，支持微信小程序、支付宝小程序和 Web 组件。

## 核心架构

### 1. 编译流程
```
源码 → AST解析 → 平台转换 → 代码生成 → 输出
```

### 2. 支持的编译目标
- **wechat**: 微信小程序
- **alipay**: 支付宝小程序  
- **web**: Web 组件 (基于 Lit Element)

### 3. 编译类型
- **miniprogram**: 小程序应用
- **component**: 组件库

## 代码结构

```
compiler/
├── src/
│   ├── compiler/
│   │   ├── ast.ts           # AST 节点定义
│   │   └── ...              # 编译器核心逻辑
│   ├── webpack-plugins/
│   │   ├── base-plugin.ts   # 基础插件
│   │   ├── helper.ts        # 插件辅助函数
│   │   └── webpack-miniprogram-plugin.ts
│   ├── options.ts           # 默认配置
│   ├── types.ts             # 类型定义
│   └── util.ts              # 工具函数
```

## 核心类型

### ICompilerOptions
```typescript
export type ICompilerOptions = {
    target: "wechat" | "alipay" | "web"
    compileType: 'miniprogram' | "component"
    outputPath?: string
    srcPath: string
    alias: XData
    plugins?: Array<any>
    // ... 更多配置项
}
```

## 关键功能

### 1. AST 处理
- 支持模板语法解析
- 组件依赖分析
- 表达式转换

### 2. Webpack 集成
- 自定义 loader 和 plugin
- 代码分割优化
- 资源处理

### 3. 平台适配
- 不同平台的 API 差异处理
- 组件行为适配
- 样式转换

## 依赖关系

- **核心依赖**: webpack, babel, postcss
- **内部依赖**: `@wosai/smart-mp-concise-shared`

## 重构建议

### 优先级：高

1. **架构解耦**
   - 分离编译器核心和 Webpack 插件
   - 抽象平台适配层
   - 模块化 AST 处理逻辑

2. **性能优化**
   - 实现增量编译
   - 优化 AST 解析性能
   - 缓存机制改进

3. **扩展性提升**
   - 插件系统重构
   - 支持自定义转换器
   - 平台扩展接口标准化

### 重构路径

1. **第一阶段**: 核心编译器抽离
   - 创建独立的编译器核心
   - 定义标准的编译接口

2. **第二阶段**: 平台适配重构
   - 抽象平台适配基类
   - 实现插件化平台支持

3. **第三阶段**: 性能和扩展性优化
   - 实现编译缓存
   - 完善插件系统
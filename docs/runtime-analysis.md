# 运行时分析

## 整体架构

运行时分为三个层次：
- **runtime-base**: 基础运行时，提供通用功能
- **runtime-mini**: 小程序运行时，适配微信/支付宝
- **runtime-web**: Web 运行时，基于 Lit Element

## runtime-base 分析

### 模块职责
提供跨平台的基础功能和类型定义。

### 核心功能
- 组件选项类型定义
- 通用工具函数
- 平台无关的基础逻辑

## runtime-mini 分析

### 模块职责
为小程序平台提供运行时支持，处理微信和支付宝的 API 差异。

### 核心功能

#### 1. 平台适配
```javascript
// 环境检测和API适配
let env = getEnv() // 'MY' | 'WX'
if (env === 'MY') {
  api = initMyApi
} else if (env === 'WX') {
  api = initWxApi
}
```

#### 2. API 统一
- 统一不同平台的 API 调用方式
- 提供 `ConciseWx` 统一接口

### 依赖关系
- **基础**: `@wosai/smart-mp-concise-runtime-base`
- **工具**: `@wosai/smart-mp-concise-shared`

## runtime-web 分析

### 模块职责
为 Web 平台提供组件运行时，基于 Lit Element 实现小程序组件在 Web 端的行为。

### 核心架构

#### 1. 组件系统
- 基于 Lit Element 的 Web Components
- 支持小程序组件语法和生命周期
- Shadow DOM 隔离

#### 2. 事件系统
```typescript
// 原生事件名称规范化
export function normalizeEventName(eventName: string): string {
  if (NATIVE_DOM_EVENTS.has(eventName.toLowerCase())) {
    return `concise-${eventName}`;
  }
  return eventName;
}
```

#### 3. 组件实例管理
```typescript
export function createInstanceDescriptor(el) {
  return {
    selectComponent: (selector) => getSelectorComponentsDescriptor(el, selector),
    setStyle: (objOrStr) => { /* 样式设置 */ },
    addClass: (className) => { /* 类名操作 */ },
    triggerEvent: (...args) => { /* 事件触发 */ }
  }
}
```

### 核心功能

#### 1. 小程序 API 模拟
- 路由系统
- 数据绑定
- 生命周期管理

#### 2. 组件通信
- 父子组件通信
- 事件冒泡处理
- 数据传递

#### 3. 样式处理
- CSS 作用域隔离
- 动态样式更新
- 响应式设计支持

### 依赖关系
- **基础**: `@wosai/smart-mp-concise-runtime-base`
- **工具**: `@wosai/smart-mp-concise-shared`
- **Web**: lit, axios, lodash

## 重构建议

### 优先级：高

#### runtime-base
1. **类型系统完善**
   - 补充完整的类型定义
   - 统一接口规范

#### runtime-mini  
1. **平台适配优化**
   - 抽象平台差异处理
   - 支持更多小程序平台

#### runtime-web
1. **性能优化**
   - 组件渲染性能提升
   - 事件处理优化
   - 内存泄漏防护

2. **功能完善**
   - 完善小程序 API 模拟
   - 改进组件生命周期
   - 优化数据绑定机制

### 重构路径

1. **统一运行时接口**
   - 定义标准的运行时接口
   - 抽象平台差异

2. **性能优化**
   - 实现虚拟 DOM 优化
   - 组件懒加载
   - 事件委托优化

3. **功能扩展**
   - 支持更多小程序特性
   - 完善开发工具集成
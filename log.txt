
> @wosai/smart-mp-concise@1.0.54 test
> npm --prefix packages/template run dev


> @wosai/smart-mp-concise-template@1.0.54 dev
> concise compile -w

{
  context: '/Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src',
  watch: false,
  devtool: false,
  mode: 'development',
  entry: {
    'index.js': '/Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src/index.js'
  },
  output: {
    path: '/Users/<USER>/Desktop/work/smart-mp-concise/packages/template/dist/web-components',
    clean: false,
    filename: '[name]',
    globalObject: 'window',
    chunkLoadingGlobal: 'concise-component-@wosai/smart-mp-concise-template-1.0.54',
    publicPath: '/',
    library: 'concise',
    libraryTarget: 'umd'
  },
  plugins: [
    DefinePlugin { definitions: [Object] },
    WebpackConciseWebPlugin { isMini: false }
  ],
  resolve: {
    extensions: [
      '.ts',    '.js',
      '.wxml',  '.less',
      '.wxss',  '.wxs',
      '.json5'
    ],
    mainFields: [ 'module', 'main' ],
    modules: [ 'node_modules' ],
    symlinks: false,
    plugins: [],
    alias: {
      '@style': '/Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src/less',
      '@wxs': '/Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src/wxs',
      '@template': '/Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src/template',
      '@wosai/smart-mp-concise-runtime': '@wosai/smart-mp-concise-runtime-web'
    }
  },
  externals: []
}
===== Webpack 配置 =====
{
  amd: undefined,
  bail: undefined,
  cache: { type: 'memory', maxGenerations: Infinity, cacheUnaffected: false },
  context: '/Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src',
  dependencies: undefined,
  devServer: undefined,
  devtool: false,
  entry: { 'index.js': { import: [Array] } },
  experiments: {
    buildHttp: undefined,
    lazyCompilation: undefined,
    futureDefaults: false,
    backCompat: true,
    syncWebAssembly: false,
    asyncWebAssembly: false,
    outputModule: false,
    layers: false,
    cacheUnaffected: false,
    css: undefined,
    topLevelAwait: true
  },
  externals: [],
  externalsPresets: {
    web: true,
    node: false,
    nwjs: false,
    electron: false,
    electronMain: false,
    electronPreload: false,
    electronRenderer: false
  },
  externalsType: 'umd',
  ignoreWarnings: undefined,
  infrastructureLogging: {
    stream: WriteStream {
      connecting: false,
      _hadError: false,
      _parent: null,
      _host: null,
      _closeAfterHandlingError: false,
      _events: [Object],
      _readableState: [ReadableState],
      _writableState: [WritableState],
      allowHalfOpen: false,
      _maxListeners: undefined,
      _eventsCount: 1,
      _sockname: null,
      _pendingData: null,
      _pendingEncoding: '',
      server: null,
      _server: null,
      columns: 130,
      rows: 23,
      _type: 'tty',
      fd: 2,
      _isStdio: true,
      destroySoon: [Function (anonymous)],
      _destroy: [Function: dummyDestroy],
      [Symbol(async_id_symbol)]: 5,
      [Symbol(kHandle)]: [TTY],
      [Symbol(lastWriteQueueSize)]: 0,
      [Symbol(timeout)]: null,
      [Symbol(kBuffer)]: null,
      [Symbol(kBufferCb)]: null,
      [Symbol(kBufferGen)]: null,
      [Symbol(shapeMode)]: true,
      [Symbol(kCapture)]: false,
      [Symbol(kSetNoDelay)]: false,
      [Symbol(kSetKeepAlive)]: false,
      [Symbol(kSetKeepAliveInitialDelay)]: 0,
      [Symbol(kBytesRead)]: 0,
      [Symbol(kBytesWritten)]: 0
    },
    level: 'info',
    debug: false,
    colors: true,
    appendOnly: false
  },
  loader: {
    target: 'web',
    environment: {
      globalThis: undefined,
      bigIntLiteral: true,
      const: true,
      arrowFunction: true,
      asyncFunction: true,
      forOf: true,
      destructuring: true,
      optionalChaining: true,
      nodePrefixForCoreModules: true,
      templateLiteral: true,
      dynamicImport: undefined,
      dynamicImportInWorker: undefined,
      module: undefined,
      document: true
    }
  },
  mode: 'development',
  module: {
    noParse: undefined,
    unsafeCache: [Function (anonymous)],
    parser: { javascript: [Object], asset: [Object], json: [Object] },
    generator: { json: [Object] },
    defaultRules: [
      [Object], [Object],
      [Object], [Object],
      [Object], [Object],
      [Object], [Object],
      [Object], [Object],
      [Object]
    ],
    rules: [ [Object], [Object], [Object], [Object] ]
  },
  name: undefined,
  node: { global: true, __filename: 'mock', __dirname: 'mock' },
  optimization: {
    minimize: false,
    runtimeChunk: { name: 'concise.r.js' },
    splitChunks: {
      chunks: 'all',
      cacheGroups: [Object],
      defaultSizeTypes: [Array],
      hidePathInfo: false,
      usedExports: false,
      minChunks: 1,
      minSize: 10000,
      minRemainingSize: 0,
      enforceSizeThreshold: 30000,
      maxAsyncRequests: Infinity,
      maxInitialRequests: Infinity,
      automaticNameDelimiter: '-'
    },
    removeAvailableModules: false,
    removeEmptyChunks: true,
    mergeDuplicateChunks: true,
    flagIncludedChunks: false,
    moduleIds: 'named',
    chunkIds: 'named',
    sideEffects: 'flag',
    providedExports: true,
    usedExports: false,
    innerGraph: false,
    mangleExports: false,
    concatenateModules: false,
    avoidEntryIife: false,
    emitOnErrors: true,
    checkWasmTypes: false,
    mangleWasmImports: false,
    portableRecords: false,
    realContentHash: false,
    minimizer: [ [Object] ],
    nodeEnv: 'development'
  },
  output: {
    assetModuleFilename: '[hash][ext][query]',
    asyncChunks: true,
    charset: true,
    chunkFilename: '[name]',
    chunkFormat: 'array-push',
    chunkLoading: 'jsonp',
    chunkLoadingGlobal: 'concise-component-@wosai/smart-mp-concise-template-1.0.54',
    chunkLoadTimeout: 120000,
    cssFilename: '[name]',
    cssChunkFilename: '[name]',
    clean: false,
    compareBeforeEmit: true,
    crossOriginLoading: false,
    devtoolFallbackModuleFilenameTemplate: undefined,
    devtoolModuleFilenameTemplate: undefined,
    devtoolNamespace: 'concise',
    environment: {
      globalThis: undefined,
      bigIntLiteral: true,
      const: true,
      arrowFunction: true,
      asyncFunction: true,
      forOf: true,
      destructuring: true,
      optionalChaining: true,
      nodePrefixForCoreModules: true,
      templateLiteral: true,
      dynamicImport: undefined,
      dynamicImportInWorker: undefined,
      module: undefined,
      document: true
    },
    enabledChunkLoadingTypes: [ 'jsonp', 'import-scripts' ],
    enabledLibraryTypes: [ 'umd' ],
    enabledWasmLoadingTypes: [ 'fetch' ],
    filename: '[name]',
    globalObject: 'window',
    hashDigest: 'hex',
    hashDigestLength: 20,
    hashFunction: 'md4',
    hashSalt: undefined,
    hotUpdateChunkFilename: '[id].[fullhash].hot-update.js',
    hotUpdateGlobal: 'webpackHotUpdateconcise',
    hotUpdateMainFilename: '[runtime].[fullhash].hot-update.json',
    ignoreBrowserWarnings: undefined,
    iife: true,
    importFunctionName: 'import',
    importMetaName: 'import.meta',
    scriptType: false,
    library: {
      type: 'umd',
      auxiliaryComment: undefined,
      amdContainer: undefined,
      export: undefined,
      name: 'concise',
      umdNamedDefine: undefined
    },
    module: false,
    path: '/Users/<USER>/Desktop/work/smart-mp-concise/packages/template/dist/web-components',
    pathinfo: true,
    publicPath: '/',
    sourceMapFilename: '[file].map[query]',
    sourcePrefix: undefined,
    strictModuleErrorHandling: false,
    strictModuleExceptionHandling: false,
    trustedTypes: undefined,
    uniqueName: 'concise',
    wasmLoading: 'fetch',
    webassemblyModuleFilename: '[hash].module.wasm',
    workerPublicPath: '',
    workerChunkLoading: 'import-scripts',
    workerWasmLoading: 'fetch'
  },
  parallelism: 100,
  performance: false,
  plugins: [
    DefinePlugin { definitions: [Object] },
    WebpackConciseWebPlugin { isMini: false, compiler: [Compiler] }
  ],
  profile: false,
  recordsInputPath: false,
  recordsOutputPath: false,
  resolve: {
    byDependency: {
      wasm: [Object],
      esm: [Object],
      loaderImport: [Object],
      worker: [Object],
      commonjs: [Object],
      amd: [Object],
      loader: [Object],
      unknown: [Object],
      undefined: [Object],
      url: [Object]
    },
    cache: true,
    modules: [ 'node_modules' ],
    conditionNames: [ 'webpack', 'development', 'browser' ],
    mainFiles: [ 'index' ],
    extensions: [
      '.ts',    '.js',
      '.wxml',  '.less',
      '.wxss',  '.wxs',
      '.json5'
    ],
    aliasFields: [],
    exportsFields: [ 'exports' ],
    roots: [
      '/Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src'
    ],
    mainFields: [ 'module', 'main' ],
    importsFields: [ 'imports' ],
    symlinks: false,
    plugins: [],
    alias: {
      '@style': '/Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src/less',
      '@wxs': '/Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src/wxs',
      '@template': '/Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src/template',
      '@wosai/smart-mp-concise-runtime': '@wosai/smart-mp-concise-runtime-web'
    }
  },
  resolveLoader: {
    cache: true,
    conditionNames: [ 'loader', 'require', 'node' ],
    exportsFields: [ 'exports' ],
    mainFields: [ 'loader', 'main' ],
    extensions: [ '.js' ],
    mainFiles: [ 'index' ]
  },
  snapshot: {
    resolveBuildDependencies: { timestamp: true, hash: true },
    buildDependencies: { timestamp: true, hash: true },
    resolve: { timestamp: true },
    module: { timestamp: true },
    immutablePaths: [],
    managedPaths: [ '/Users/<USER>/Desktop/work/smart-mp-concise/node_modules/' ],
    unmanagedPaths: []
  },
  stats: {},
  target: 'web',
  watch: false,
  watchOptions: {}
}
Server started: http://192.168.3.23:8082
▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄
█ ▄▄▄▄▄ ██▄▄ ▀ ██▄█ ▄▄▄▄▄ █
█ █   █ █▀▄  █▀▀▀ █ █   █ █
█ █▄▄▄█ █▄▀ █▄▀▄█▀█ █▄▄▄█ █
█▄▄▄▄▄▄▄█▄▀▄█ █ ▀▄█▄▄▄▄▄▄▄█
█▄ ▀█▀▄▄▄▀▄ █▄ █  ▀██  ▀▀██
█▀█▄█▀ ▄▀▀▄▀ ▄▄█ ▀█▄▄▀ █▄ █
█  █▀ █▄██  █▀▀ ▀▄▄▄████▀▄█
█ ██ █ ▄▀▄▀▄█▀█▀▄▀▄█▄▀▄▀▄ █
█▄█▄▄▄█▄▄▀▀▄█▄▄▀▀ ▄▄▄ █ ███
█ ▄▄▄▄▄ █ ▄▀▀▄▄▄█ █▄█ ▄██ █
█ █   █ ██▀█▄  █▄▄▄  ▄ ▄▀▀█
█ █▄▄▄█ █▀▄▀▀ ██▀▀▄▀▀▀█▄█ █
█▄▄▄▄▄▄▄█▄██▄██▄▄▄█▄██▄██▄█

assets by path npm/ 3.24 MiB 179 assets
assets by chunk 208 KiB (id hint: vendor)
  assets by path components/goods/*.js 75.5 KiB
    asset components/goods/index.less.js 70.4 KiB [emitted] (name: components/goods/index.less.js) (id hint: vendor)
    + 3 assets
  assets by path components/cell/*.js 8.45 KiB
    asset components/cell/index.less.js 3.41 KiB [emitted] (name: components/cell/index.less.js) (id hint: vendor)
    + 3 assets
  assets by path components/info/*.js 8.54 KiB
    asset components/info/index.js 3 KiB [emitted] (name: components/info/index.js) (id hint: vendor)
    asset components/info/index.less.js 2.82 KiB [emitted] (name: components/info/index.less.js) (id hint: vendor)
    + 2 assets
  asset concise.c.js 115 KiB [emitted] (name: concise.c.js) (id hint: vendor)
asset concise.r.js 43.3 KiB [emitted] (name: concise.r.js)
asset index.js 9.24 KiB [emitted] (name: index.js)
Entrypoint index.js 3.49 MiB = 194 assets
runtime modules 28.8 KiB 15 modules
orphan modules 922 bytes [orphan] 1 module
modules by path ../ 2.98 MiB
  modules by path ../node_modules/@wosai/ 2.62 MiB 110 modules
  modules by path ../../../node_modules/ 369 KiB 78 modules
modules by path ./ 53 KiB
  javascript modules 52.8 KiB 12 modules
  json modules 178 bytes 3 modules
asset modules 22.8 KiB
  data:application/x-font-ttf;charset=utf-8;base64,AAEAAAAKAIAAAwAg.. 4.06 KiB [built] [code generated]
  data:application/x-font-ttf;charset=utf-8;base64,AAEAAAAKAIAAAwAg.. 3.76 KiB [built] [code generated]
  data:application/x-font-ttf;charset=utf-8;base64,AAEAAAAKAIAAAwAg.. 4.04 KiB [built] [code generated]
  data:application/x-font-ttf;charset=utf-8;base64,AAEAAAAKAIAAAwAg.. 10.9 KiB [built] [code generated]
crypto (ignored) 15 bytes [built] [code generated]
buffer (ignored) 15 bytes [built] [code generated]
webpack 5.99.6 compiled successfully in 4088 ms
assets by status 192 KiB [cached] 26 assets
assets by status 109 KiB [emitted]
  assets by info 64.2 KiB [immutable]
    asset concise_r_js.29c3020a9308e6ae1be0.hot-update.json 43 KiB [emitted] [immutable] [hmr]
    asset concise.r.js.29c3020a9308e6ae1be0.hot-update.js 21.2 KiB [emitted] [immutable] [hmr] (name: concise.r.js)
  asset concise.r.js 42.4 KiB [emitted] (name: concise.r.js)
  asset index.js 2.54 KiB [emitted] (name: index.js)
Entrypoint index.js 258 KiB = 29 assets
cached modules 151 KiB [cached] 29 modules
runtime modules 28.4 KiB 12 modules

ERROR in index.js
Module not found: Error: Can't resolve '/Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src/index.js' in '/Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src'
resolve '/Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src/index.js' in '/Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src'
  using description file: /Users/<USER>/Desktop/work/smart-mp-concise/packages/template/package.json (relative path: ./src)
    Field 'browser' doesn't contain a valid alias configuration
    root path /Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src
      using description file: /Users/<USER>/Desktop/work/smart-mp-concise/packages/template/package.json (relative path: ./src/Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src/index.js)
        no extension
          Field 'browser' doesn't contain a valid alias configuration
          /Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src/Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src/index.js doesn't exist
        .ts
          Field 'browser' doesn't contain a valid alias configuration
          /Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src/Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src/index.js.ts doesn't exist
        .js
          Field 'browser' doesn't contain a valid alias configuration
          /Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src/Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src/index.js.js doesn't exist
        .wxml
          Field 'browser' doesn't contain a valid alias configuration
          /Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src/Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src/index.js.wxml doesn't exist
        .less
          Field 'browser' doesn't contain a valid alias configuration
          /Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src/Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src/index.js.less doesn't exist
        .wxss
          Field 'browser' doesn't contain a valid alias configuration
          /Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src/Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src/index.js.wxss doesn't exist
        .wxs
          Field 'browser' doesn't contain a valid alias configuration
          /Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src/Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src/index.js.wxs doesn't exist
        .json5
          Field 'browser' doesn't contain a valid alias configuration
          /Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src/Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src/index.js.json5 doesn't exist
        as directory
          /Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src/Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src/index.js doesn't exist
    using description file: /Users/<USER>/Desktop/work/smart-mp-concise/packages/template/package.json (relative path: ./src/index.js)
      no extension
        Field 'browser' doesn't contain a valid alias configuration
        /Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src/index.js doesn't exist
      .ts
        Field 'browser' doesn't contain a valid alias configuration
        /Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src/index.js.ts doesn't exist
      .js
        Field 'browser' doesn't contain a valid alias configuration
        /Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src/index.js.js doesn't exist
      .wxml
        Field 'browser' doesn't contain a valid alias configuration
        /Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src/index.js.wxml doesn't exist
      .less
        Field 'browser' doesn't contain a valid alias configuration
        /Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src/index.js.less doesn't exist
      .wxss
        Field 'browser' doesn't contain a valid alias configuration
        /Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src/index.js.wxss doesn't exist
      .wxs
        Field 'browser' doesn't contain a valid alias configuration
        /Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src/index.js.wxs doesn't exist
      .json5
        Field 'browser' doesn't contain a valid alias configuration
        /Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src/index.js.json5 doesn't exist
      as directory
        /Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src/index.js doesn't exist

webpack 5.99.6 compiled with 1 error in 61 ms

> @wosai/smart-mp-concise@1.0.54 test
> npm --prefix packages/template run dev


> @wosai/smart-mp-concise-template@1.0.54 dev
> concise compile -w

{
  context: '/Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src',
  watch: false,
  devtool: false,
  mode: 'development',
  entry: {
    'web.index.js': '/Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src/web.index.js'
  },
  output: {
    path: '/Users/<USER>/Desktop/work/smart-mp-concise/packages/template/dist/web-components',
    clean: false,
    filename: '[name]',
    globalObject: 'window',
    chunkLoadingGlobal: 'concise-component-@wosai/smart-mp-concise-template-1.0.54',
    publicPath: '/',
    library: 'concise',
    libraryTarget: 'umd'
  },
  plugins: [
    DefinePlugin { definitions: [Object] },
    WebpackConciseWebPlugin { isMini: false }
  ],
  resolve: {
    extensions: [
      '.ts',    '.js',
      '.wxml',  '.less',
      '.wxss',  '.wxs',
      '.json5'
    ],
    mainFields: [ 'module', 'main' ],
    modules: [ 'node_modules' ],
    symlinks: false,
    plugins: [],
    alias: {
      '@wosai/smart-mp-concise-runtime': '@wosai/smart-mp-concise-runtime-web'
    }
  },
  externals: []
}
===== Webpack 配置 =====
{
  amd: undefined,
  bail: undefined,
  cache: { type: 'memory', maxGenerations: Infinity, cacheUnaffected: false },
  context: '/Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src',
  dependencies: undefined,
  devServer: undefined,
  devtool: false,
  entry: { 'web.index.js': { import: [Array] } },
  experiments: {
    buildHttp: undefined,
    lazyCompilation: undefined,
    futureDefaults: false,
    backCompat: true,
    syncWebAssembly: false,
    asyncWebAssembly: false,
    outputModule: false,
    layers: false,
    cacheUnaffected: false,
    css: undefined,
    topLevelAwait: true
  },
  externals: [],
  externalsPresets: {
    web: true,
    node: false,
    nwjs: false,
    electron: false,
    electronMain: false,
    electronPreload: false,
    electronRenderer: false
  },
  externalsType: 'umd',
  ignoreWarnings: undefined,
  infrastructureLogging: {
    stream: WriteStream {
      connecting: false,
      _hadError: false,
      _parent: null,
      _host: null,
      _closeAfterHandlingError: false,
      _events: [Object],
      _readableState: [ReadableState],
      _writableState: [WritableState],
      allowHalfOpen: false,
      _maxListeners: undefined,
      _eventsCount: 1,
      _sockname: null,
      _pendingData: null,
      _pendingEncoding: '',
      server: null,
      _server: null,
      columns: 130,
      rows: 29,
      _type: 'tty',
      fd: 2,
      _isStdio: true,
      destroySoon: [Function (anonymous)],
      _destroy: [Function: dummyDestroy],
      [Symbol(async_id_symbol)]: 5,
      [Symbol(kHandle)]: [TTY],
      [Symbol(lastWriteQueueSize)]: 0,
      [Symbol(timeout)]: null,
      [Symbol(kBuffer)]: null,
      [Symbol(kBufferCb)]: null,
      [Symbol(kBufferGen)]: null,
      [Symbol(shapeMode)]: true,
      [Symbol(kCapture)]: false,
      [Symbol(kSetNoDelay)]: false,
      [Symbol(kSetKeepAlive)]: false,
      [Symbol(kSetKeepAliveInitialDelay)]: 0,
      [Symbol(kBytesRead)]: 0,
      [Symbol(kBytesWritten)]: 0
    },
    level: 'info',
    debug: false,
    colors: true,
    appendOnly: false
  },
  loader: {
    target: 'web',
    environment: {
      globalThis: undefined,
      bigIntLiteral: true,
      const: true,
      arrowFunction: true,
      asyncFunction: true,
      forOf: true,
      destructuring: true,
      optionalChaining: true,
      nodePrefixForCoreModules: true,
      templateLiteral: true,
      dynamicImport: undefined,
      dynamicImportInWorker: undefined,
      module: undefined,
      document: true
    }
  },
  mode: 'development',
  module: {
    noParse: undefined,
    unsafeCache: [Function (anonymous)],
    parser: { javascript: [Object], asset: [Object], json: [Object] },
    generator: { json: [Object] },
    defaultRules: [
      [Object], [Object],
      [Object], [Object],
      [Object], [Object],
      [Object], [Object],
      [Object], [Object],
      [Object]
    ],
    rules: [ [Object], [Object], [Object], [Object] ]
  },
  name: undefined,
  node: { global: true, __filename: 'mock', __dirname: 'mock' },
  optimization: {
    minimize: false,
    runtimeChunk: { name: 'concise.r.js' },
    splitChunks: {
      chunks: 'all',
      cacheGroups: [Object],
      defaultSizeTypes: [Array],
      hidePathInfo: false,
      usedExports: false,
      minChunks: 1,
      minSize: 10000,
      minRemainingSize: 0,
      enforceSizeThreshold: 30000,
      maxAsyncRequests: Infinity,
      maxInitialRequests: Infinity,
      automaticNameDelimiter: '-'
    },
    removeAvailableModules: false,
    removeEmptyChunks: true,
    mergeDuplicateChunks: true,
    flagIncludedChunks: false,
    moduleIds: 'named',
    chunkIds: 'named',
    sideEffects: 'flag',
    providedExports: true,
    usedExports: false,
    innerGraph: false,
    mangleExports: false,
    concatenateModules: false,
    avoidEntryIife: false,
    emitOnErrors: true,
    checkWasmTypes: false,
    mangleWasmImports: false,
    portableRecords: false,
    realContentHash: false,
    minimizer: [ [Object] ],
    nodeEnv: 'development'
  },
  output: {
    assetModuleFilename: '[hash][ext][query]',
    asyncChunks: true,
    charset: true,
    chunkFilename: '[name]',
    chunkFormat: 'array-push',
    chunkLoading: 'jsonp',
    chunkLoadingGlobal: 'concise-component-@wosai/smart-mp-concise-template-1.0.54',
    chunkLoadTimeout: 120000,
    cssFilename: '[name]',
    cssChunkFilename: '[name]',
    clean: false,
    compareBeforeEmit: true,
    crossOriginLoading: false,
    devtoolFallbackModuleFilenameTemplate: undefined,
    devtoolModuleFilenameTemplate: undefined,
    devtoolNamespace: 'concise',
    environment: {
      globalThis: undefined,
      bigIntLiteral: true,
      const: true,
      arrowFunction: true,
      asyncFunction: true,
      forOf: true,
      destructuring: true,
      optionalChaining: true,
      nodePrefixForCoreModules: true,
      templateLiteral: true,
      dynamicImport: undefined,
      dynamicImportInWorker: undefined,
      module: undefined,
      document: true
    },
    enabledChunkLoadingTypes: [ 'jsonp', 'import-scripts' ],
    enabledLibraryTypes: [ 'umd' ],
    enabledWasmLoadingTypes: [ 'fetch' ],
    filename: '[name]',
    globalObject: 'window',
    hashDigest: 'hex',
    hashDigestLength: 20,
    hashFunction: 'md4',
    hashSalt: undefined,
    hotUpdateChunkFilename: '[id].[fullhash].hot-update.js',
    hotUpdateGlobal: 'webpackHotUpdateconcise',
    hotUpdateMainFilename: '[runtime].[fullhash].hot-update.json',
    ignoreBrowserWarnings: undefined,
    iife: true,
    importFunctionName: 'import',
    importMetaName: 'import.meta',
    scriptType: false,
    library: {
      type: 'umd',
      auxiliaryComment: undefined,
      amdContainer: undefined,
      export: undefined,
      name: 'concise',
      umdNamedDefine: undefined
    },
    module: false,
    path: '/Users/<USER>/Desktop/work/smart-mp-concise/packages/template/dist/web-components',
    pathinfo: true,
    publicPath: '/',
    sourceMapFilename: '[file].map[query]',
    sourcePrefix: undefined,
    strictModuleErrorHandling: false,
    strictModuleExceptionHandling: false,
    trustedTypes: undefined,
    uniqueName: 'concise',
    wasmLoading: 'fetch',
    webassemblyModuleFilename: '[hash].module.wasm',
    workerPublicPath: '',
    workerChunkLoading: 'import-scripts',
    workerWasmLoading: 'fetch'
  },
  parallelism: 100,
  performance: false,
  plugins: [
    DefinePlugin { definitions: [Object] },
    WebpackConciseWebPlugin { isMini: false, compiler: [Compiler] }
  ],
  profile: false,
  recordsInputPath: false,
  recordsOutputPath: false,
  resolve: {
    byDependency: {
      wasm: [Object],
      esm: [Object],
      loaderImport: [Object],
      worker: [Object],
      commonjs: [Object],
      amd: [Object],
      loader: [Object],
      unknown: [Object],
      undefined: [Object],
      url: [Object]
    },
    cache: true,
    modules: [ 'node_modules' ],
    conditionNames: [ 'webpack', 'development', 'browser' ],
    mainFiles: [ 'index' ],
    extensions: [
      '.ts',    '.js',
      '.wxml',  '.less',
      '.wxss',  '.wxs',
      '.json5'
    ],
    aliasFields: [],
    exportsFields: [ 'exports' ],
    roots: [
      '/Users/<USER>/Desktop/work/smart-mp-concise/packages/template/src'
    ],
    mainFields: [ 'module', 'main' ],
    importsFields: [ 'imports' ],
    symlinks: false,
    plugins: [],
    alias: {
      '@wosai/smart-mp-concise-runtime': '@wosai/smart-mp-concise-runtime-web'
    }
  },
  resolveLoader: {
    cache: true,
    conditionNames: [ 'loader', 'require', 'node' ],
    exportsFields: [ 'exports' ],
    mainFields: [ 'loader', 'main' ],
    extensions: [ '.js' ],
    mainFiles: [ 'index' ]
  },
  snapshot: {
    resolveBuildDependencies: { timestamp: true, hash: true },
    buildDependencies: { timestamp: true, hash: true },
    resolve: { timestamp: true },
    module: { timestamp: true },
    immutablePaths: [],
    managedPaths: [ '/Users/<USER>/Desktop/work/smart-mp-concise/node_modules/' ],
    unmanagedPaths: []
  },
  stats: {},
  target: 'web',
  watch: false,
  watchOptions: {}
}

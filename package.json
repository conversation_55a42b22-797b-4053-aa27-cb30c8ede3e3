{"name": "@wosai/smart-mp-concise", "version": "2.0.3", "description": "", "main": "index.js", "workspaces": {"packages": ["packages/*"]}, "scripts": {"test": "npm --prefix packages/template run dev", "tb": "npm --prefix packages/template run build", "build": "node scripts/build.js", "release": "node scripts/release.js"}, "keywords": [], "author": "<PERSON>", "license": "ISC", "devDependencies": {"@rollup/plugin-commonjs": "^18.0.0", "@rollup/plugin-json": "^4.0.0", "@rollup/plugin-node-resolve": "^11.2.1", "@rollup/plugin-replace": "^2.3.4", "brotli": "^1.3.2", "chalk": "^4.1.0", "conventional-changelog-cli": "^2.0.31", "csstype": "^3.0.3", "enquirer": "^2.3.2", "esbuild": "^0.14.35", "eslint": "^8.0.1", "eslint-plugin-jest": "26.1.5", "execa": "^4.0.2", "fs-extra": "^9.0.1", "husky": "^7.0.4", "jest": "^27.1.0", "lint-staged": "^10.2.10", "lodash": "^4.17.15", "marked": "^4.0.10", "minimist": "^1.2.0", "miniprogram-api-typings": "^3.12.2", "npm-run-all": "^4.1.5", "rollup": "~2.38.5", "rollup-plugin-node-builtins": "^2.1.2", "rollup-plugin-node-globals": "^1.4.0", "rollup-plugin-polyfill-node": "^0.6.2", "rollup-plugin-typescript2": "^0.27.2", "ts-jest": "^27.0.5", "tslib": "^2.4.0", "typescript": "^4.6.4", "@types/node": "^22.3.0", "@wosai/smart-mp-concise-cli": "workspace:*", "@wosai/smart-mp-concise-compiler": "workspace:*", "@wosai/smart-mp-concise-runtime-web": "workspace:*", "@wosai/smart-mp-concise-runtime-base": "workspace:*", "@wosai/smart-mp-concise-shared": "workspace:*"}, "dependencies": {}}